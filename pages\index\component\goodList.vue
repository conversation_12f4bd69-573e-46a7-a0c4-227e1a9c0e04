<template>
	<view :style="{ marginTop: mbConfig + 'rpx',background: themeColor,borderRadius: bgStyle+'rpx'}" v-if="tempArr.length">
		<view class="index-product-wrapper" :style="{ background: themeColor,borderRadius: bgStyle+'rpx'}">
			<!-- 单列 -->
			<block v-if="itemStyle == 0">	
				<view class="list-box listA" :class="tempArr.length > 0 ? 'fadeIn on' : ''">
					<view class="item acea-row row-between" :class="'cont'+conStyle" v-for="(item, index) in tempArr" :key="index" @click="goDetail(item)">
						<view class="pictrue" :class="'cont'+conStyle">	
							<easy-loadimage mode="widthFix" :image-src="item.image"></easy-loadimage>
							<view v-if="item.stock == 0" class="sell_out">已售罄</view>
							<view v-if="item.border_pic" :style="{ backgroundImage: `url(${item.border_pic})` }" class="border-picture"></view>
						</view>
						<view class="text-info acea-row row-column-between">
							<view>
								<view v-if="titleShow" class="title line2">{{ item.store_name }}</view>
								<view class="merchant_info">
									<view v-if="item.merchant && item.merchant.type_name" :style="'background:'+labelColor" class="font-bg-red">{{item.merchant.type_name}}</view>
									<view class="txt" :style="'border-color:'+priceColor+';color:'+priceColor+';'" v-if="item.issetCoupon && couponShow">领券</view>
									<view class="txt delivery" v-if="item.delivery_free">包邮</view>
								</view>
							</view>
							<view v-if="priceShow" class="price acea-row" :style="'color:'+priceColor">
								<view class="semiBold">
									<priceFormat :price="item.price" weight intSize="40" floatSize="26" labelSize="26"></priceFormat>
								</view>
							</view>			
						</view>
						<view class="add-cart acea-row row-middle row-center" :style="'background:'+labelColor" @click.stop='selecAttr(item)'>
							<text class="iconfont icon-ic_ShoppingCart1"></text>
						</view>
					</view>
				</view>
			</block>
			<!-- 两列 -->
			<block v-if="itemStyle == 1">
				<view class="list-box listS" :class="tempArr.length > 0 ? 'fadeIn on' : ''">
					<view class="item" v-for="(item, index) in tempArr" :key="index" @click="goDetail(item)" :class="'bg'+conStyle">
						<view class="pictrue picture1" :class="'cont'+conStyle">
							<!-- <image :src="item.image" mode=""></image> -->
							<easy-loadimage mode="widthFix" :image-src="item.image"></easy-loadimage>
							<view v-if="item.stock == 0" class="sell_out">已售罄</view>
							<view v-if="item.border_pic" :style="{ backgroundImage: `url(${item.border_pic})` }" class="border-picture"></view>
						</view>
						<view class="text-info">
							<view v-if="titleShow" class="title line1">{{ item.store_name }}</view>
							<view v-if="priceShow" class="price acea-row" :style="'color:'+priceColor">
								<view class="semiBold">
									<priceFormat :price="item.price" weight intSize="40" floatSize="26" labelSize="26"></priceFormat>
								</view>
							</view>
						</view>
						<view class="add-cart acea-row row-middle row-center" :style="'background:'+labelColor" @click.stop='selecAttr(item)'>
							<text class="iconfont icon-ic_ShoppingCart1"></text>
						</view>
					</view>
				</view>
			</block>
			<!-- 三列 -->
			<block v-if="itemStyle == 2">
				<view class="list-box listB" :class="tempArr.length > 0 ? 'fadeIn on' : ''">
					<view class="item" v-for="(item, index) in tempArr" :key="index" @click="goDetail(item)">
						<view class="pictrue" :class="'cont'+conStyle">		
							<easy-loadimage mode="widthFix" :image-src="item.image"></easy-loadimage>
							<view v-if="item.stock == 0" class="sell_out">已售罄</view>
							<view v-if="item.border_pic" :style="{ backgroundImage: `url(${item.border_pic})` }" class="border-picture"></view>
						</view>
						<view class="text-info" style="display: flex; flex-direction: column; justify-content: space-between;">
							<view v-if="titleShow" class="title line1">{{ item.store_name }}</view>
							<view v-if="priceShow" class="price">
								<view v-if="priceShow" :style="'color:'+priceColor" class="semiBold">
									<priceFormat :price="item.price" weight intSize="40" floatSize="26" labelSize="26"></priceFormat>
								</view>
							</view>
						</view>
					</view>
				</view>
			</block>
			<!--大图-->
			<block v-if="itemStyle == 3">
				<view class="list-box listC" :class="tempArr.length > 0 ? 'fadeIn on' : ''">
					<view class="item" v-for="(item, index) in tempArr" :key="index" @click="goDetail(item)" :style="'border-radius:'+bgStyle+'rpx;'">
						<view class="pictrue" :class="'cont'+conStyle">
							<easy-loadimage mode="widthFix" :image-src="item.image"></easy-loadimage>
						</view>
						<view class="text-info" style="display: flex; flex-direction: column; justify-content: space-between;">
							<view v-if="titleShow" class="title line2">{{ item.store_name }}</view>	
							<view v-if="priceShow || opriceShow" class="price">
								<view v-if="priceShow" :style="'color:'+priceColor" class="semiBold">
									<priceFormat :price="item.price" weight intSize="40" floatSize="26" labelSize="26"></priceFormat>
								</view>
							</view>
						</view>
						<view class="add-cart acea-row row-middle row-center" :style="'background:'+labelColor" @click.stop='selecAttr(item)'>
							<text class="iconfont icon-ic_ShoppingCart1"></text>
						</view>
					</view>
				</view>
			</block>
		</view>
		<!-- 组件 -->
		<productWindow 
		:attr="attr" 
		:isShow='1' 
		:iSplus='1' 
		:destri="0" 
		:isList="true" 
		:isTab="true" 
		:isCustom="isCustom" @myevent="onMyEvent" @ChangeAttr="ChangeAttr" @ChangeCartNum="ChangeCartNum"
		 @attrVal="attrVal" @goCat="goCat" @iptCartNum="iptCartNum" id='product-window' :payLimit="pay_limit" :maxCount="max_count" :minCount="min_count" :svipPrice="svipPrice" :image="storeInfo.image"></productWindow>
	</view>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import { getProductData } from '@/api/api.js';
import productWindow from '@/components/productWindow';
import util from '@/utils/util.js';
export default {
	components: {
		productWindow
	},
	name: 'goodList',
	props: {
		dataConfig: {
			type: Object,
			default: () => {}
		},
		merId: {
			type: String || Number,
			default: ''
		},
		isCustom: {
			type: Boolean,
			default: () => false
		}
	},
	data() {
		return {
			tempArr: [],
			mbConfig: this.dataConfig.mbConfig.val*2,
			numConfig: this.dataConfig.numConfig.val ? this.dataConfig.numConfig.val : this.dataConfig.numConfig.val,
			themeColor: this.dataConfig.themeColor.color[0].item,
			priceColor: this.dataConfig.fontColor.color[0].item,
			labelColor: this.dataConfig.labelColor.color[0].item,
			itemStyle: this.dataConfig.itemStyle.type,
			sortType: this.dataConfig.goodsSort.type,
			conStyle: this.dataConfig.conStyle.type,
			bgStyle: this.dataConfig.bgStyle.type ? '24' : '0',
			type: this.dataConfig.tabConfig.tabVal || 0,
			selectId: this.dataConfig.selectConfig.activeValue || 0,
			productIds: this.dataConfig.goodsList.ids || [],
			titleShow: this.dataConfig.titleShow.val,
			priceShow: this.dataConfig.priceShow.val,
			couponShow: this.dataConfig.couponShow.val,
			diy_id: this.dataConfig.did,
			unique: this.dataConfig.timestamp,
			max_count: 0,
			min_count: 0,
			pay_limit: 1,
			svipData: {},
			svipPrice: false,
			attr: {
				cartAttr: false,
				productAttr: [],
				productSelect: {}
			},
			storeInfo: {},
			cart_num: 1, //购买数量
			isOpen: false, //是否打开属性组件
			selectedArr: [],
			productValue: [], //系统属性
			attrValue: "",
		};
	},
	created() {},
	mounted() {
		this.productslist();
	},
	methods: {
		productslist() {
			let data = {};
			if (this.type == 1) {
				data = {
					diy_id: this.diy_id,
					unique: this.unique,
					mer_id: this.merId,
					product_ids: this.productIds.toString(),
					limit: this.productIds.length,
				};
			} else {
				data = {
					diy_id: this.diy_id,
					unique: this.unique,
					mer_id: this.merId,
					order: this.sortType == 2 ? 'price_asc' : this.sortType == 1 ? 'sales' : '',
					limit: this.numConfig,
				};
				if(this.merId){
					data.mer_cate_id = (this.selectId&&this.selectId.toString()) || ''
				}else{
					data.cate_pid = (this.selectId&&this.selectId.toString()) || ''
				}
			}
			getProductData(data).then(res => {
				this.tempArr = res.data.list;
			});
		},
		goDetail(item) {
			this.$emit('detail', item);
		},
		/**
		 * 打开属性插件
		 */
		selecAttr: function(item) {
			let that = this;
			let type = item.product ? item.product.type : item.type
			if((item.product_type == 0 && type != 0) || item.product_type > 0 || item.mer_form_id){
				that.goDetail(item);
			}else{
				that.storeInfo = item;
				util.getProductSpecs(item.spu_id,function(data){
					that.$set(that, "attr", data.attr);
					that.$set(that.attr.productSelect,"store_name",item.store_name);
					that.$set(that, 'productValue', data.productValue);
					that.$set(that, "attrValue", data.attrValue);
					// that.$set(that, 'svipPrice', item.show_svip_info && item.show_svip_info.show_svip_price || false);
					that.$set(that.attr, 'cartAttr', true);
					that.$set(that, 'isOpen', true);
				})
			}		
		},
		onMyEvent: function() {
			this.$set(this.attr, 'cartAttr', false);
			this.$set(this, 'isOpen', false);
		},
		attrVal(val) {
			this.$set(this.attr.productAttr[val.indexw], 'index', this.attr.productAttr[val.indexw].attr_values[val.indexn]);
		},
		/**
		 * 购物车手动填写
		 *
		 */
		iptCartNum: function(e) {	
			this.$set(this.attr.productSelect, 'cart_num', e);
		},
		/**
		 * 属性变动赋值
		 *
		 */
		ChangeAttr: function(res) {
			let productSelect = this.productValue[res];
			this.attr.productSelect = {...this.attr.productSelect,...productSelect}
			if(!productSelect || productSelect.stock <= 0){
				this.$set(this.attr.productSelect, "stock", 0);
				this.$set(this.attr.productSelect, "unique", "");
				this.$set(this.attr.productSelect, "cart_num", 0);
			}
		},
		/**
		 * 购物车数量加和数量减
		 *
		 */
		ChangeCartNum: function(changeValue) {
			//changeValue:是否 加|减
			//获取当前变动属性
			let productSelect = this.productValue[this.attrValue];
			let that = this
			util.ChangeCartNum(productSelect,this.attr,changeValue,function(stock){
				this.$set(that.attr.productSelect, "cart_num", stock);
			})
		},
		/*
		 * 加入购物车
		 */
		goCat: function() {
			let that = this,
			productSelect = that.productValue[this.attrValue];
			//打开属性
			if (that.attrValue) {
				//默认选中了属性，但是没有打开过属性弹窗还是自动打开让用户查看默认选中的属性
				that.attr.cartAttr = !that.isOpen ? true : false;
			} else {
				if (that.isOpen) that.attr.cartAttr = true;
				else
				that.attr.cartAttr = !that.attr.cartAttr;
			}
			//只有关闭属性弹窗时进行加入购物车
			if (that.attr.cartAttr === true && that.isOpen === false)
				return (that.isOpen = true);	
			that.isOpen = that.attr.cartAttr = true;
			//如果有属性,没有选择,提示用户选择
			if (
				that.attr.productAttr.length &&
				that.isOpen === true &&
				productSelect.stock == 0
			)
				return that.$util.Tips({
					title: "产品库存不足，请选择其它"
				});
			if (that.attr.productSelect.cart_num == 0) {
				return that.$util.Tips({
					title: '购买个数不能为0！'
				})
			}
			let q = {
				is_new: 0,
				product_id: that.storeInfo.product_id,
				cart_num: that.attr.productSelect.cart_num,
				product_attr_unique: that.attr.productSelect !== undefined ? that.attr.productSelect.unique : "",
				product_type: that.storeInfo.product_type,
			};
			util.addCart(q,function(){
				that.isOpen = that.attr.cartAttr = false;
			})
		},
	}
};
</script>

<style lang="scss" scoped>
.index-product-wrapper {
	margin: 0 20rpx;
	.list-box {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;	
		.item {
			width: 327rpx;
			margin-bottom: 20rpx;
			overflow: hidden;
			position: relative;
			&.on {
				border-radius: 0;
			}
			.pictrue,/deep/image,/deep/.easy-loadimage,/deep/uni-image {
				width: 100%;
				display: block;
				position: relative;	
			}
			.picture1,/deep/.picture1 image,/deep/.picture1 .easy-loadimage,/deep/.picture1 uni-image {			
				height: 346rpx;
				position: relative;
			}
			.sell_out {
				display: flex;
				width: 150rpx;
				height: 150rpx;
				align-items: center;
				justify-content: center;
				border-radius: 100%;
				background: rgba(0,0,0,.6);
				color: #fff;
				font-size: 30rpx;
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translateX(-50%) translateY(-50%);
				&::before{
					content: "";
					display: block;
					width: 140rpx;
					height: 140rpx;
					border-radius: 100%;
					border: 1px dashed #fff;
					position: absolute;
				}
			}
			.cont1,/deep/.cont1 image,/deep/.cont1 .easy-loadimage,/deep/.cont1 uni-image,.cont1 .border-picture{
				border-radius: 16rpx;
			}
			.text-info {
				padding: 10rpx 20rpx 0;
				.title {
					color: #222222;
				}
				.price {
					display: flex;
					margin-top: 20rpx;
					font-size: 26rpx;
					align-items: center;
					text {
						font-size: 36rpx;
						font-weight: 550;
					}
					.ot-price{
						color: #aaa;
						font-size: 26rpx;
						text-decoration: line-through;
						margin-left: 6rpx;
						font-weight: normal;
						margin-top: 10rpx;
					}	
				}
			}
			.pictrue {
				position: relative;
			}		
			.border-picture {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				border-radius: 16rpx;
				background: center/cover no-repeat;
			}
		}
		.merchant_info{
			display: flex;
			align-items: center;
			margin-top: 20rpx;
			.merchant_type{
				color: #fff;
				line-height: 30rpx;
				padding: 0 10rpx;
				border-radius: 2rpx;
				font-size: 22rpx;
			}
			.txt {
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 0 4rpx;
				height: 28rpx;
				margin-left: 15rpx;
				border: 1px solid $theme-color;
				border-radius: 4rpx;
				font-size: 20rpx;
				font-weight: normal;
				&.delivery{
					color: #FF9000;
					border-color: #FF9000;
				}
			}
		}
		&.on {
			display: flex;
		}
		&.listA {	
			.item {
				width: 100%;
				background: #fff;
				padding: 20rpx;
				&.cont1 {
					border-radius: 16rpx;
				}
				.pictrue,/deep/image,/deep/.easy-loadimage,/deep/uni-image {
					width: 250rpx;
					height: 250rpx;		
				}
				.sell_out {
					width: 124rpx;
					height: 124rpx;
					&::before{
						width: 110rpx;
						height: 110rpx;
					}
				}
				.text-info {
					width: 400rpx;
					height: 250rpx;
					padding: 10rpx 0 0;
				}
			}
		}
		&.listB {
			justify-content: inherit;
			.item {
				width: 31.3%;
				margin-right: 3.05%;
				margin-bottom: 0;
				.pictrue,/deep/image,/deep/.easy-loadimage,/deep/uni-image {
					height: 220rpx;	
				}
				.sell_out {
					width: 110rpx;
					height: 110rpx;
					&::before{
						width: 100rpx;
						height: 100rpx;
					}
				}
				&:nth-child(3n) {
					margin-right: 0;
				}
				.price{
					font-size: 20rpx;
					text{
						font-size: 28rpx;
					}
				}
				.text-info{
					padding: 10rpx 4rpx 20rpx;	
				}
			}
		}
		&.listC{
			.item{
				width: 100%;
				.pictrue,/deep/image,/deep/.easy-loadimage,/deep/uni-image{
					height: 320rpx;			
				}
				.price{
					margin-top: 20rpx;
					font-size: 40rpx;
					display: flex;
					align-items: center;
				}
			}
		}
		&.listS{
			padding-top: 20rpx;
			.item{
				background: #ffffff;
				width: calc(50% - 10rpx);
			}	
			.price{
				font-size: 40rpx;
				display: flex;
				align-items: baseline;
			}
			.cont1,/deep/.cont1 image,/deep/.cont1 .easy-loadimage,/deep/.cont1 uni-image,.cont1 .border-picture{
				border-radius: 16rpx 16rpx 0 0;
			}
			.bg1{
				border-radius: 16rpx;
			}
			.text-info{
				padding: 20rpx;	
			}
		}
	}
}
.add-cart{
	bottom: 20rpx;
	right: 20rpx;
}
</style>
