<template>
  <view class="store-detail">
    <MyNavbar title="门店详情" />
    <!-- Banner -->
    <view class="store-detail__banner" v-if="storeInfo.mer_id">
      <image :src="storeInfo.mer_banner" class="store-detail__banner-img" mode="widthFix" />
    </view>
    <view class="store-detail__content" v-if="storeInfo.mer_id">
      <!-- 门店信息 -->
      <view class="store-detail__store-info card">
        <view class="store-detail__store-info-content">
          <view class="store-detail__store-name">
            {{ storeInfo.mer_name }}
            <view class="store-detail__store-status">营业中</view>
          </view>
          <view class="store-detail__base-row">
            <text class="iconfont icon-icon_clock-2"></text>
            <view>
              <view class="store-detail__label">营业时间</view>
              <view class="store-detail__desc">
                周{{ storeInfo.mer_take_day.join(',') }} {{ storeInfo.mer_take_time.join('-') }}
              </view>
            </view>
          </view>
          <view class="store-detail__base-row">
            <text class="iconfont icon-ic_location51"></text>
            <view>
              <view class="store-detail__label">门店地址</view>
              <view class="store-detail__desc">
                <view class="store-detail__address">
                  {{ storeInfo.mer_address }}
                  <text
                    class="iconfont icon-icon_copy"
                    @click="handleCopy(storeInfo.mer_address)"
                  ></text>
                  <text class="store-detail__distance">距离你{{ storeInfo.distance }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 热门推荐 -->
      <view class="title-row">
        <view class="title">热门推荐</view>
        <view class="more">
          查看更多
          <text class="iconfont icon-ic_rightarrow"></text>
        </view>
      </view>
      <view class="store-detail__hot-recommend card">
        <scroll-view scroll-x class="store-detail__hot-tabs">
          <view
            v-for="tab in hotTabs"
            :key="tab.store_category_id"
            :class="[
              'store-detail__hot-tab',
              activeHotTab === tab.store_category_id ? 'active' : '',
            ]"
            @tap="onHotTabChange(tab.store_category_id)"
          >
            {{ tab.cate_name }}
          </view>
        </scroll-view>
        <view class="store-detail__hot-list" v-if="hotList.length">
          <block v-for="item in hotList" :key="item.product_id">
            <view class="store-detail__hot-item" @click="toShoppingPage">
              <image :src="item.image" mode="aspectFill" class="store-detail__hot-img"></image>
              <view class="store-detail__hot-name">{{ item.store_name }}</view>
              <view class="store-detail__hot-price">
                {{ item.price }}
                <text class="store-detail__hot-discount">{{ item.discount }}折</text>
              </view>
            </view>
          </block>
        </view>
        <view class="store-detail__hot-emp" v-else>暂无服务</view>
      </view>
      <!-- 发型师 -->
      <view class="title-row">
        <view class="title">发型师</view>
        <view class="more" @click="toStylistPage">
          查看更多
          <text class="iconfont icon-ic_rightarrow"></text>
        </view>
      </view>
      <view class="store-detail__hairstylist card">
        <view class="store-detail__hairstylist-list">
          <block v-for="stylist in hairstylists" :key="stylist.service_id">
            <view class="store-detail__hairstylist-item" @click="toDetails(stylist)">
              <image
                class="store-detail__hairstylist-avatar"
                :src="stylist.avatar"
                mode="aspectFill"
              ></image>
              <view class="store-detail__hairstylist-info">
                <view class="store-detail__hairstylist-info-top">
                  <view class="store-detail__hairstylist-name">{{ stylist.nickname }}</view>
                  <RoleTag :role="stylist.level_name" />
                  <view class="store-detail__hairstylist-rate">满意值 {{ stylist.manyizhi }}</view>
                </view>
                <view class="store-detail__hairstylist-actions">
                  <view
                    class="store-detail__hairstylist-action"
                    @click.stop="openSchedulingPopup(stylist)"
                  >
                    <image
                      src="/static/images/icon-paiban.png"
                      class="store-detail__hairstylist-action-icon"
                      mode="widthFix"
                    />
                    <text>排班</text>
                  </view>
                  <view class="store-detail__hairstylist-action">
                    <image
                      src="/static/images/icon-meifa.png"
                      class="store-detail__hairstylist-action-icon"
                      mode="widthFix"
                    />
                    <text>作品</text>
                  </view>
                </view>
              </view>
              <button
                class="store-detail__appointment-btn"
                @click.stop="handleAppointment(stylist)"
              >
                去预约
              </button>
            </view>
          </block>
        </view>
      </view>
      <!-- 精选作品 -->
      <block v-if="worksList.length">
        <view class="title-row">
          <view class="title">精选作品</view>
          <view class="more" @click="toWorksPage">
            查看更多
            <text class="iconfont icon-ic_rightarrow"></text>
          </view>
        </view>
        <view class="store-detail__works card">
          <scroll-view scroll-x class="store-detail__works-list">
            <block v-for="item in worksList" :key="item.id">
              <view class="store-detail__works-item" @click="toWorksDetails(item)">
                <image
                  :src="item.files[0]"
                  mode="aspectFill"
                  class="store-detail__works-img"
                ></image>
                <view class="store-detail__works-name">{{ item.title }}</view>
                <view class="store-detail__works-tag-box">
                  <view class="store-detail__works-tag" v-for="tag in item.tags" :key="tag">
                    {{ tag }}
                  </view>
                </view>
              </view>
            </block>
          </scroll-view>
        </view>
      </block>

      <!-- 服务评价 -->
      <block v-if="replyInfo.count">
        <view class="title-row">
          <view class="title">服务评价</view>
          <view class="more" @click="toCommentsPage">
            查看更多
            <text class="iconfont icon-ic_rightarrow"></text>
          </view>
        </view>
        <view class="store-detail__comments card">
          <view class="store-detail__section-header">
            <view class="store-detail__section-title">评价 ({{ replyInfo.count }})</view>
            <view class="store-detail__section-more">
              <image src="/static/images/icon-collect.png" mode="scaleToFill" class="collect" />
              98%好评率
              <text class="iconfont icon-ic_rightarrow"></text>
            </view>
          </view>
          <view class="store-detail__rate-row">
            <block v-for="(tag, index) of replyInfo.tagsList" :key="index">
              <view class="store-detail__rate-item">{{ tag.name }} {{ tag.count }}</view>
            </block>
          </view>
          <block v-for="item in replyInfo.list" :key="item.id">
            <view class="store-detail__comment-item">
              <image :src="item.avatar" class="store-detail__comment-avatar"></image>
              <view class="store-detail__comment-content">
                <view class="store-detail__comment-header">
                  <view class="store-detail__comment-user">
                    <view class="user">用户{{ item.nickname }}</view>
                    <view class="id">ID: {{ item.uid }}</view>
                  </view>
                  <view class="store-detail__comment-satisfy">
                    <Expression :score="item.product_score" />
                  </view>
                </view>
                <view class="store-detail__comment-tags">
                  <block v-for="(tag, index) of item.tagsList" :key="index">
                    <text class="store-detail__comment-tag">{{ tag }}</text>
                  </block>
                </view>
                <view class="store-detail__comment-text">
                  {{ item.comment }}
                </view>
                <view class="store-detail__comment-footer">
                  <view class="store-detail__comment-name">
                    <view class="tag center">
                      <image src="/static/images/icon-user.png" mode="widthFix" class="img"></image>
                    </view>
                    {{ item.service_name }}
                  </view>
                  <view class="store-detail__comment-time">{{ item.create_time }}</view>
                </view>
              </view>
            </view>
          </block>
        </view>
      </block>

      <!-- 底部按钮 -->
      <view class="store-detail__bottom-bar">
        <view class="store-detail__bottom-item">
          <image
            src="/static/images/icon-kefu.png"
            mode="widthFix"
            class="store-detail__bottom-icon"
          ></image>
          <view class="store-detail__bottom-text">客服咨询</view>
        </view>
        <view class="store-detail__bottom-item">
          <image
            src="/static/images/icon-dianhua.png"
            mode="widthFix"
            class="store-detail__bottom-icon"
          ></image>
          <view class="store-detail__bottom-text">联系门店</view>
        </view>
        <button
          class="store-detail__bottom-btn center"
          hover-class="button-hover"
          @click="toAppointmentPage"
        >
          立即预约
        </button>
      </view>
    </view>
    <!-- 排班 -->
    <Scheduling ref="schedulingRef" />
  </view>
</template>

<script>
import RoleTag from '@/components/roleTags/roleTags.vue'
import MyNavbar from '@/components/my-navbar/my-navbar.vue'
import Expression from '@/components/expression/expression.vue'
import Scheduling from '@/components/scheduling/scheduling'
import storeInfoMixin from '@/mixins/storeInfo.js'

import {
  getStoreServicesList,
  getServerCategorys,
  getHairstylist,
  getReplyList,
  getWorksList,
} from '@/api/hairdressing.js'

export default {
  components: { RoleTag, MyNavbar, Expression, Scheduling },
  mixins: [storeInfoMixin],
  data() {
    return {
      hairstylists: [],
      activeHotTab: '',
      hotTabs: [],
      hotList: [],
      worksList: [],
      replyInfo: {}, //评论数据
    }
  },
  onLoad(options) {
    if (options.store) {
      const storeInfo = JSON.parse(options.store)
      // 使用 mixin 方法存储门店信息
      this.setStoreInfo(storeInfo)
      this.loadHotTabs()
      this.loadStoreServicesList()
      this.loadStylist()
      this.loadReplyList()
      this.loadWorksList()
    }
  },
  methods: {
    onHotTabChange(value) {
      this.activeHotTab = value
      this.loadStoreServicesList()
    },
    handleAppointment(stylist) {
      // 预约逻辑
      uni.navigateTo({
        url: `/pages/hairdressing/appointment/index?info=${JSON.stringify(
          this.storeInfo,
        )}&service_id=${stylist.service_id}`,
      })
    },
    toShoppingPage() {
      uni.navigateTo({
        url: '/pages/hairdressing/place_order/index?info=' + JSON.stringify(this.storeInfo),
      })
    },
    //处理复制
    handleCopy(text) {
      uni.setClipboardData({
        data: text,
        success() {
          uni.showToast({
            title: '复制成功',
            icon: 'success',
          })
        },
        fail(err) {
          console.log('🚀 ~ fail ~ err:', err)
          uni.showToast({
            title: '复制失败',
            icon: 'none',
          })
        },
      })
    },
    //加载服务分类
    loadHotTabs() {
      getServerCategorys().then((res) => {
        this.hotTabs = [{ cate_name: '全部', store_category_id: '' }, ...res.data]
      })
    },
    //加载 服务列表
    loadStoreServicesList() {
      getStoreServicesList(this.storeInfo.mer_id, {
        cate_id: this.activeHotTab,
      }).then((res) => {
        res.data.list.forEach((item) => {
          item.discount = ((item.product.price / item.product.ot_price) * 10).toFixed(1)
        })
        this.hotList = res.data.list
        console.log('🚀 ~ loadStoreServicesList ~ res:', res)
      })
    },
    //加载发型师
    loadStylist() {
      getHairstylist(this.storeInfo.mer_id).then((res) => {
        this.hairstylists = res.data.list
      })
    },
    //加载评论列表
    loadReplyList() {
      getReplyList(this.storeInfo.mer_id, { page: 1, limit: 10 }).then((res) => {
        res.data.tagsList = Object.keys(res.data.tag_stats).map((item) => {
          return {
            name: item,
            count: res.data.tag_stats[item],
          }
        })
        res.data.list.forEach((item) => {
          if (item.tags && item.tags !== null) {
            console.log('🚀 ~ res.data.list.forEach ~ item:', item.tags)
            item.tagsList = item.tags.split(',')
          } else {
            item.tagsList = []
          }
        })
        this.replyInfo = res.data
      })
    },
    //加载作品列表
    loadWorksList() {
      getWorksList(this.storeInfo.mer_id, { page: 1, limit: 10 }).then((res) => {
        this.worksList = res.data.list
      })
    },
    openSchedulingPopup(stylist) {
      this.$refs.schedulingRef.open(stylist)
    },
    toStylistPage() {
      uni.navigateTo({
        url: '/pages/hairdressing/hairstylist/index?mer_id=' + this.storeInfo.mer_id,
      })
    },
    toWorksPage() {
      uni.navigateTo({
        url: '/pages/hairdressing/selected_works/index?mer_id=' + this.storeInfo.mer_id,
      })
    },
    toWorksDetails(works) {
      uni.navigateTo({
        url: '/pages/hairdressing/work_details/index?info=' + JSON.stringify(works),
      })
    },
    //发型师详情
    toDetails(v) {
      uni.navigateTo({ url: '/pages/hairdressing/hairstylist/details?info=' + JSON.stringify(v) })
    },
    toAppointmentPage() {
      uni.navigateTo({
        url: `/pages/hairdressing/appointment/index?info=${JSON.stringify(this.storeInfo)}`,
      })
    },
    toCommentsPage() {
      uni.navigateTo({
        url: '/pages/hairdressing/service_evaluation/index?mer_id=' + this.storeInfo.mer_id,
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.store-detail {
  background-color: #f7f7f7;
  min-height: 100vh;

  .title-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 16rpx 0;
    .title {
      font-size: 30rpx;
      font-weight: bold;
      height: 54rpx;
      background: url('http://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/22cb6202506231642544308.png')
        no-repeat;
      background-size: 100% 34rpx;
      background-position: bottom center;
    }
    .more {
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      .iconfont {
        font-size: 22rpx;
        color: #e0e0e0;
      }
    }
  }

  &__banner {
    .store-detail__banner-img {
      width: 100%;
      height: 528rpx;
      display: block;
    }
  }

  &__content {
    padding: 14rpx 30rpx 200rpx;
    margin-top: -124rpx;
    position: relative;
    z-index: 8;
  }

  /* 公用卡片样式 */
  .card {
    border-radius: 30rpx;
    padding: 30rpx 20rpx 20rpx;
    margin-bottom: 24rpx;
    background-color: #ffffff;
  }

  /* 门店信息 */
  &__store-info {
    background: linear-gradient(90deg, #ffffff 0%, #ffffff 35%, #fffaf2 100%) !important;
    .store-detail__store-info-content {
      .store-detail__store-name {
        font-size: 30rpx;
        font-weight: bold;
        display: flex;
        align-items: center;
        .store-detail__store-status {
          width: 76rpx;
          height: 28rpx;
          background: linear-gradient(0deg, #2f2c24, #755e18);
          border-radius: 8rpx;
          font-weight: 400;
          font-size: 20rpx;
          color: #ffffff;
          margin-left: 16rpx;
          text-align: center;
          line-height: 28rpx;
        }
      }
      .store-detail__base-row {
        display: flex;
        margin-top: 20rpx;
        .iconfont {
          margin-right: 10rpx;
          font-size: 24rpx;
          font-weight: bold;
          padding-top: 4rpx;
        }
        .store-detail__label {
          font-weight: 400;
          font-size: 24rpx;
          color: #333333;
        }
        .store-detail__desc {
          font-weight: 400;
          font-size: 20rpx;
          color: #999999;
          .store-detail__address {
            display: flex;
            align-items: center;
            margin-top: 8rpx;
            font-size: 20rpx;
            .iconfont {
              margin-left: 8rpx;
              color: #cecece;
              font-size: 22rpx;
            }
            .store-detail__distance {
              color: #333333;
              margin-left: 10rpx;
            }
          }
        }
      }
    }
  }

  /* 热门推荐 */
  &__hot-recommend {
    .store-detail__hot-tabs {
      // display: flex;
      // align-items: center;
      // column-gap: 60rpx;
      width: 100%;
      white-space: nowrap;
      margin-bottom: 20rpx;
      .store-detail__hot-tab {
        display: inline-block;
        font-size: 26rpx;
        color: #999;
        position: relative;
        margin-right: 60rpx;
        z-index: 8;
        &.active {
          color: #333;
          font-weight: 600;
          &::after {
            content: '';
            display: block;
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            height: 9rpx;
            background: linear-gradient(-90deg, #ffffff, #c9a063);
            border-radius: 5rpx;
            z-index: -1;
          }
        }
      }
    }
    .store-detail__hot-list {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 42rpx;
      .store-detail__hot-item {
        width: 100%;
        .store-detail__hot-img {
          width: 188rpx;
          height: 188rpx;
          border-radius: 12rpx;
          display: block;
        }
        .store-detail__hot-name {
          font-weight: 500;
          font-size: 24rpx;
          color: #333333;
          margin: 10rpx 0 20rpx;
        }
        .store-detail__hot-price {
          font-weight: 500;
          font-size: 24rpx;
          color: #a01c1c;
          &::before {
            content: '￥';
            font-size: 18rpx;
          }
          .store-detail__hot-discount {
            font-size: 20rpx;
            color: #a01c1c;
            border-radius: 4rpx;
            padding: 2rpx 6rpx;
            margin: 0 10rpx;
            background: #f7e9e9;
            border-radius: 3rpx;
            position: relative;
            &::before {
              content: '';
              position: absolute;
              left: -6rpx;
              top: 50%;
              transform: translateY(-50%);
              width: 0;
              height: 0;
              border-style: solid;
              border-width: 8rpx 8rpx 8rpx 0;
              border-color: transparent #f7e9e9 transparent transparent;
            }
          }
        }
      }
    }
    .store-detail__hot-emp {
      text-align: center;
      color: #ccc;
      padding: 30rpx 0;
    }
  }

  /* 发型师 */
  &__hairstylist {
    .store-detail__hairstylist-list {
      .store-detail__hairstylist-item {
        display: flex;
        align-items: center;
        padding: 20rpx 0;
        border-bottom: 1rpx solid #f2f3f7;
        &:last-child {
          border-bottom: none;
        }
        .store-detail__hairstylist-avatar {
          width: 98rpx;
          height: 98rpx;
          border-radius: 50%;
          margin-right: 20rpx;
          flex-shrink: 0;
        }
        .store-detail__hairstylist-info {
          flex: 1;
          .store-detail__hairstylist-info-top {
            display: flex;
            align-items: center;
            margin-bottom: 20rpx;
          }

          .store-detail__hairstylist-name {
            font-size: 28rpx;
            font-weight: bold;
            color: #333;
          }
          .store-detail__hairstylist-rate {
            width: 98rpx;
            height: 28rpx;
            background: linear-gradient(-34deg, #ffffff, #f8eedc, #c9a063);
            border-radius: 6rpx;
            font-size: 18rpx;
            color: #a27630;
            margin: 10rpx 0;
            text-align: center;
            line-height: 28rpx;
          }
          .store-detail__hairstylist-actions {
            display: flex;
            align-items: center;
            gap: 15rpx;
            .store-detail__hairstylist-action {
              display: flex;
              align-items: center;
              background-color: #f0f0f0;
              padding: 5rpx 15rpx;
              border-radius: 6rpx;
              font-size: 22rpx;
              color: #999;
              .store-detail__hairstylist-action-icon {
                width: 28rpx;
                height: 28rpx;
                margin-right: 10rpx;
              }
            }
          }
        }
        .store-detail__appointment-btn {
          background-color: #d5a162;
          color: #fff;
          padding: 14rpx 30rpx;
          border-radius: 30rpx;
          font-size: 24rpx;
          margin-left: 20rpx;
          flex-shrink: 0;
        }
      }
    }
  }

  /* 精选作品 */
  &__works {
    .store-detail__works-list {
      width: 100%;
      display: flex;
      overflow-x: auto;
      white-space: nowrap;
      .store-detail__works-item {
        width: 200rpx;
        display: inline-block;
        margin-right: 20rpx;
        &:last-child {
          margin-right: 0;
        }
        .store-detail__works-img {
          width: 180rpx;
          height: 240rpx;
          border-radius: 12rpx;
        }
        .store-detail__works-name {
          font-weight: 400;
          font-size: 26rpx;
          color: #333333;
          margin: 8rpx 0 10rpx;
        }
        .store-detail__works-tag-box {
          display: flex;
          flex-wrap: wrap;
          gap: 10rpx;
        }
        .store-detail__works-tag {
          padding: 10rpx;
          font-weight: 400;
          font-size: 20rpx;
          color: #c9a063;
          background: #fff4e3;
          border-radius: 8rpx;
        }
      }
    }
  }

  /* 评价 */
  &__comments {
    .store-detail__section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20rpx;
      .store-detail__section-title {
        font-weight: 600;
        font-size: 26rpx;
        color: #333333;
      }
      .store-detail__section-more {
        font-weight: 400;
        font-size: 24rpx;
        color: #c9a063;
        display: flex;
        align-items: center;
        column-gap: 10rpx;
        .collect {
          width: 22rpx;
          height: 22rpx;
        }
        .iconfont {
          font-size: 22rpx;
          font-weight: bold;
          color: #d19e58;
        }
      }
    }

    .store-detail__rate-row {
      display: flex;
      margin-bottom: 40rpx;
      column-gap: 14rpx;
      .store-detail__rate-item {
        background: #fff4e3;
        border-radius: 20rpx;
        font-weight: 400;
        font-size: 20rpx;
        color: #c9a063;
        padding: 10rpx 20rpx;
      }
    }
    .store-detail__comment-item {
      display: flex;
      align-items: flex-start;
      margin-top: 10rpx;
      .store-detail__comment-avatar {
        width: 68rpx;
        height: 68rpx;
        border-radius: 50%;
        margin-right: 20rpx;
      }
      .store-detail__comment-content {
        flex: 1;
        .store-detail__comment-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .user {
            font-weight: 500;
            font-size: 24rpx;
            color: #333333;
            margin-bottom: 10rpx;
          }
          .id {
            font-weight: 400;
            font-size: 22rpx;
            color: #666666;
          }
        }

        .store-detail__comment-tags {
          display: flex;
          align-items: center;
          column-gap: 20rpx;
          margin: 20rpx 0;
          .store-detail__comment-tag {
            background: #fafafa;
            border-radius: 20rpx;
            padding: 4rpx 12rpx;
            font-weight: 400;
            font-size: 20rpx;
            color: #adadad;
          }
        }
        .store-detail__comment-text {
          font-weight: 400;
          font-size: 20rpx;
          color: #666666;
          margin-bottom: 20rpx;
        }
        .store-detail__comment-footer {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .store-detail__comment-name {
            display: flex;
            align-items: center;
            column-gap: 8rpx;
            .tag {
              width: 23rpx;
              height: 23rpx;
              background: #c9a063;
              border-radius: 4rpx;
              .img {
                width: 12rpx;
                height: 16rpx;
              }
            }
            background: #fafafa;
            border-radius: 8rpx;
            padding: 10rpx;
            font-weight: 400;
            font-size: 20rpx;
            color: #adadad;
          }
          .store-detail__comment-time {
            font-weight: 400;
            font-size: 20rpx;
            color: #adadad;
          }
        }
      }
    }
  }

  /* 底部按钮 */
  &__bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #ffffff;
    padding: 20rpx 24rpx;
    display: flex;
    align-items: center;
    padding-bottom: calc(constant(safe-area-inset-bottom) + 20rpx);
    padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
    border-top: 1rpx solid #f0f0f0;
    z-index: 99;
    .store-detail__bottom-item {
      text-align: center;
      color: #666666;
      margin-right: 50rpx;
      .store-detail__bottom-icon {
        width: 42rpx;
        height: 42rpx;
      }
      .store-detail__bottom-text {
        font-size: 20rpx;
        color: #999;
        margin-top: 6rpx;
      }
    }
    .store-detail__bottom-btn {
      width: 428rpx;
      height: 78rpx;
      background: #c9a063;
      border-radius: 39rpx;
      font-size: 30rpx;
      color: #fff;
    }
  }
}
</style>
