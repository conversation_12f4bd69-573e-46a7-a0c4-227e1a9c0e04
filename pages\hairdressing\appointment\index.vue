<template>
  <view class="page-container">
    <view class="card-box store-box">
      <view class="store-info">
        <image :src="storeInfo.mer_avatar" mode="aspectFill" class="store-cover"></image>
        <view class="store-content">
          <view class="name">
            {{ storeInfo.mer_name }}
            <text class="iconfont icon-ic_rightarrow"></text>
          </view>
          <view class="address">
            {{ storeInfo.mer_address }}
            <text class="iconfont icon-icon_copy" @click="handleCopy(storeInfo.mer_address)"></text>
          </view>
          <view class="distance">距离你 {{ storeInfo.distance }}</view>
        </view>
      </view>
    </view>
    <!-- 服务项目 -->
    <view class="service-item">
      <view class="service-item__name">服务项目</view>
      <view class="service-item__desc">
        <picker
          :value="selectedServiceIndex"
          :range="serverList"
          range-key="cate_name"
          @change="onServiceChange"
          :disabled="serverList.length === 0"
        >
          <view class="acea-row row-between-wrapper">
            <view v-if="selectedService && serverList.length > 0">
              {{ selectedService.cate_name }}
            </view>
            <view v-else class="placeholder">
              {{ serverList.length === 0 ? '加载中...' : '请选择服务项目' }}
            </view>
            <text class="iconfont icon-ic_rightarrow"></text>
          </view>
        </picker>
      </view>
    </view>

    <view class="card-box">
      <view class="title">请选择发型师</view>
      <scroll-view scroll-x class="scroll-view">
        <view class="list">
          <view
            :class="['item', stylistId == item.service_id ? 'on' : '']"
            v-for="(item, index) in hairstylists"
            :key="index"
            @click="onStylist(item.service_id)"
          >
            <view class="avatar-box">
              <image :src="item.avatar" mode="aspectFill" class="avatar"></image>
              <text class="check iconfont icon-a-ic_CompleteSelect"></text>
              <view v-if="item.manyizhi" class="satisfaction center">
                满意度{{ item.manyizhi }}
              </view>
            </view>
            <view v-if="item.nickname" class="name">{{ item.nickname }}</view>
            <view v-if="item.label" class="label">
              {{ item.label }}
            </view>
            <view v-if="item.level_name" class="role center">
              <RoleTag :role="item.level_name" />
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <view class="card-box">
      <view class="title">请选择预约时间</view>
      <view class="time-list-head">
        <view
          :class="['item', dateId == item.id ? 'on' : '']"
          v-for="item in currentDateList"
          :key="item.id"
          @tap="onDate(item.id)"
        >
          <view class="week">{{ item.week }}</view>
          <view class="day">{{ item.day }}</view>
        </view>
      </view>
      <view class="time-list">
        <view
          :class="['item', item.user_id > 0 ? 'disabled' : '', timeId == item.id ? 'on' : '']"
          v-for="item in time"
          :key="item.id"
          @tap="onTime(item)"
        >
          <view class="time">{{ item.time }}</view>
          <view v-if="item.user_id > 0" class="state">已满</view>
        </view>
      </view>
      <view class="rest-box" v-if="!time.length">
        <image class="rest-img" src="/static/images/rest-day.png" />
      </view>
    </view>

    <view class="bottom-bar">
      <view class="item">
        <image src="/static/images/icon-kefu.png" mode="widthFix" class="icon"></image>
        <view class="text">客服咨询</view>
      </view>
      <!-- 打电话 -->
      <view class="item" @click="handleCall">
        <image src="/static/images/icon-dianhua.png" mode="widthFix" class="icon"></image>
        <view class="text">联系门店</view>
      </view>
      <button hover-class="button-hover" class="btn center" @click="handleSubmit">立即预约</button>
    </view>
  </view>
</template>

<script>
import RoleTag from '@/components/roleTags/roleTags.vue'
import {
  getServerCategorys,
  getHairstylist,
  getAppointment,
  postAppointment,
} from '@/api/hairdressing.js'
export default {
  components: { RoleTag },
  data() {
    return {
      storeInfo: {},
      // 选中状态
      stylistId: 0,
      dateId: 0,
      timeId: null,
      selectedServiceIndex: 0,
      selectedService: null,

      // 数据列表
      hairstylists: [],
      serverList: [],
      defaultDateList: [], // 默认7天日期列表
      appointmentDataList: [], // API返回的排班数据
      time: [],

      // 其他状态
      timetable: '',
    }
  },

  computed: {
    // 当前使用的日期列表
    currentDateList() {
      return this.isWalkInMode ? this.defaultDateList : this.appointmentDateList
    },

    // 当前使用的日期列表（用于获取选中日期）
    appointmentDateList() {
      if (!this.appointmentDataList.length) return []

      const weekNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      const today = new Date()
      const todayStr = today.toISOString().split('T')[0]

      return this.appointmentDataList.map((item, index) => {
        const date = new Date(item.day)
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const weekDay = date.getDay()

        let weekText = ''
        if (item.day === todayStr) {
          weekText = '今天'
        } else if (
          item.day === new Date(today.getTime() + 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        ) {
          weekText = '明天'
        } else {
          weekText = weekNames[weekDay]
        }

        return {
          id: index,
          week: weekText,
          day: `${month}.${day}`,
          fullDate: item.day,
        }
      })
    },

    // 是否为到店分配模式
    isWalkInMode() {
      return this.stylistId === '' || this.stylistId === 0 || this.stylistId === null
    },

    // 默认时间段
    defaultTimeSlots() {
      const slots = []
      for (let hour = 9; hour <= 17; hour++) {
        for (let minute = 0; minute < 60; minute += 30) {
          const timeStr = `${hour.toString().padStart(2, '0')}:${minute
            .toString()
            .padStart(2, '0')}`
          slots.push({
            id: slots.length,
            time: timeStr,
            user_id: 0,
            disabled: false,
          })
        }
      }
      return slots
    },

    // 获取选中的日期
    selectedDate() {
      const dateList = this.currentDateList
      return dateList[this.dateId]?.fullDate || ''
    },
  },
  onLoad(options) {
    if (options.service_id) {
      this.stylistId = options.service_id
    }
    if (options.info) {
      this.storeInfo = JSON.parse(options.info)
      this.initData()
    }
  },
  methods: {
    // 初始化所有数据
    async initData() {
      this.initDefaultDateList()
      await Promise.all([this.loadServices(), this.loadStylists()])
      this.loadTimeSlots()
    },

    // 重置选中状态
    resetSelection(resetTime = true, resetDate = false) {
      if (resetTime) {
        this.timeId = null
        this.timetable = ''
      }
      if (resetDate) {
        this.dateId = 0
      }
    },

    // 事件处理
    onStylist(id) {
      if (this.stylistId === id) return

      this.stylistId = id
      this.resetSelection(true, true)
      this.loadTimeSlots()
    },

    onDate(id) {
      if (this.dateId === id) return

      this.dateId = id
      this.resetSelection(true, false)
      this.renderTimeSlots()
    },

    onTime(item) {
      if (item.disabled || item.user_id > 0) return

      this.timeId = item.id
      this.timetable = item.time
    },

    onServiceChange(e) {
      this.selectedServiceIndex = e.detail.value
      this.selectedService = this.serverList[e.detail.value]
    },

    handleCopy(text) {
      uni.setClipboardData({
        data: text,
        success: () => {
          uni.showToast({ title: '复制成功', icon: 'success' })
        },
      })
    },
    // 数据加载方法
    async loadServices() {
      try {
        const res = await getServerCategorys()
        this.serverList = res.data || []

        // 设置默认选中第一项
        if (this.serverList.length > 0) {
          this.selectedServiceIndex = 0
          this.selectedService = this.serverList[0]
        }
      } catch (error) {
        console.error('加载服务分类失败:', error)
        this.serverList = []
      }
    },
    async loadStylists() {
      try {
        const res = await getHairstylist(this.storeInfo.mer_id)
        const walkInOption = {
          service_id: '',
          nickname: '到店分配',
          avatar: '/static/images/icon-tag-other.png',
          level_name: null,
          manyizhi: '9.8',
          label: '',
        }

        this.hairstylists = [walkInOption, ...(res.data.list || [])]
      } catch (error) {
        console.error('加载发型师失败:', error)
        this.hairstylists = [
          {
            service_id: '',
            nickname: '到店分配',
            avatar: '/static/images/icon-tag-other.png',
            level_name: null,
            manyizhi: '9.8',
            label: '到店分配',
          },
        ]
      }
    },
    // 时间段管理
    loadTimeSlots() {
      if (this.isWalkInMode) {
        this.time = this.defaultTimeSlots
      } else {
        this.loadAppointmentData()
      }
    },
    async loadAppointmentData() {
      if (!this.defaultDateList.length) return

      try {
        const firstDate = this.defaultDateList[0].fullDate
        const lastDate = this.defaultDateList[this.defaultDateList.length - 1].fullDate
        const params = {
          service_id: this.stylistId,
          day: `${firstDate} ~ ${lastDate}`,
        }

        const res = await getAppointment(params)
        this.appointmentDataList = res.data || []
        this.renderTimeSlots()
      } catch (error) {
        console.error('加载排班数据失败:', error)
        this.appointmentDataList = []
        this.time = []
      }
    },
    renderTimeSlots() {
      if (this.isWalkInMode) {
        this.time = this.defaultTimeSlots
        return
      }

      if (!this.appointmentDataList.length) {
        this.time = []
        return
      }

      const selectedDate = this.selectedDate
      if (!selectedDate) {
        this.time = []
        return
      }

      // 找到当前选中日期的数据
      const dayData = this.appointmentDataList.find((item) => item.day === selectedDate)
      if (dayData && dayData.data && dayData.data[0] && dayData.data[0].timetable) {
        this.time = dayData.data[0].timetable
      } else {
        this.time = []
      }
    },
    // 初始化默认日期列表
    initDefaultDateList() {
      const today = new Date()
      const dateList = []
      const weekNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']

      for (let i = 0; i < 7; i++) {
        const date = new Date(today)
        date.setDate(today.getDate() + i)

        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const weekDay = date.getDay()

        let weekText = ''
        if (i === 0) {
          weekText = '今天'
        } else if (i === 1) {
          weekText = '明天'
        } else {
          weekText = weekNames[weekDay]
        }

        dateList.push({
          id: i,
          week: weekText,
          day: `${month}.${day}`,
          fullDate: `${date.getFullYear()}-${month}-${day}`,
        })
      }

      this.defaultDateList = dateList
    },
    // 表单验证和提交
    validateForm() {
      if (!this.selectedService) {
        this.$util.Tips({ title: '请选择服务项目' })
        return false
      }

      if (this.timeId === null) {
        this.$util.Tips({ title: '请选择预约时间' })
        return false
      }

      return true
    },

    async handleSubmit() {
      if (!this.validateForm()) return

      const submitData = {
        day: this.selectedDate,
        store_service_id: this.stylistId,
        timetable: this.timetable,
        store_name: this.selectedService.cate_name, //预约的服务分类
      }

      try {
        uni.showLoading({ title: '预约中...', mask: true })

        await postAppointment(submitData)

        uni.hideLoading()
        uni.showToast({ title: '预约成功', icon: 'success' })

        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      } catch (error) {
        uni.hideLoading()
        this.$util.Tips({
          title: error || '预约失败，请重试',
        })
      }
    },
    handleCall() {
      if (!this.storeInfo.phone) {
        this.$util.Tips({ title: '暂无联系电话' })
        return
      }

      uni.makePhoneCall({
        phoneNumber: this.storeInfo.phone,
        fail: () => {
          this.$util.Tips({ title: '拨打电话失败' })
        },
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.page-container {
  background-color: #f7f7f7;
  padding: 14rpx 30rpx 200rpx;
}
.store-box {
  background: linear-gradient(90deg, #ffffff 0%, #ffffff 35%, #fffaf2 100%) !important;
}
.service-item {
  padding: 40rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: white;
  border-radius: 30rpx;
  margin-bottom: 10rpx;
  &__name {
    font-weight: 500;
    font-size: 28rpx;
    color: #333333;
  }
  &__desc {
    font-weight: 400;
    font-size: 26rpx;
    color: #333333;
  }
}
.card-box {
  border-radius: 30rpx;
  padding: 30rpx 20rpx 20rpx;
  margin-bottom: 10rpx;
  background-color: #ffffff;

  .title {
    font-size: 30rpx;
    font-weight: bold;
    margin-bottom: 30rpx;
  }
  .store-info {
    display: flex;
    align-items: center;
    .store-cover {
      width: 96rpx;
      height: 96rpx;
      border-radius: 8rpx;
    }
    .store-content {
      flex: 1;
      margin-left: 20rpx;
      .name {
        font-size: 30rpx;
        font-weight: bold;
        .iconfont {
          color: #999;
          margin-left: 10rpx;
          font-size: 24rpx;
          font-weight: bold;
        }
      }

      .address,
      .distance {
        font-weight: 400;
        font-size: 20rpx;
        margin-top: 8rpx;
        .iconfont {
          margin-left: 8rpx;
          color: #cecece;
          font-size: 22rpx;
        }
      }
      .distance {
        color: #c9a063;
      }
    }
  }
  .scroll-view {
    .list {
      display: flex;
      align-items: center;
      // justify-content: space-between;
      padding: 20rpx 0;
      &.date {
        .item {
          flex-shrink: 0;
          text-align: center;
          &.on {
            .week,
            .day {
              color: #c9a063;
            }
          }
          .week {
            font-size: 28rpx;
          }
          .day {
            color: #999999;
            font-size: 24rpx;
            margin-top: 10rpx;
          }
        }
      }
      .item {
        margin-right: 20rpx;
        text-align: center;
        border: 2rpx solid transparent;
        position: relative;
        background: #ffffff;
        box-shadow: 0rpx 4rpx 17rpx 1rpx rgba(194, 193, 192, 0.22);
        border-radius: 18rpx;
        width: 128rpx;
        height: 218rpx;
        &:last-child {
          margin-right: 0;
        }
        &:first-child {
          .avatar-box {
            height: 148rpx;
            background: #f3eedd;
            border-radius: 18rpx;
          }
          .label {
            font-weight: 500;
            font-size: 24rpx;
          }
        }
        &.on {
          // border-color: #cdaa82;
          .iconfont {
            color: #c9a063 !important;
          }
        }
        .avatar-box {
          position: relative;
          width: 128rpx;
          height: 128rpx;
          .avatar {
            width: 128rpx;
            height: 128rpx;
            border-radius: 30rpx 30rpx 0 0;
          }
          .check {
            position: absolute;
            top: 16rpx;
            right: 16rpx;
            color: #cccccc;
            border-radius: 50%;
            background-color: #fff;
            width: 24rpx;
            height: 24rpx;
            font-size: 32rpx;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .satisfaction {
            position: absolute;
            left: 0;
            bottom: 0;
            width: 108rpx;
            height: 28rpx;
            background: linear-gradient(-34deg, #ffffff, #e2c99e);
            border-radius: 6rpx 6rpx 0rpx 0rpx;
            font-family: PingFang SC;
            font-weight: 300;
            font-size: 18rpx;
            color: #a27630;
          }
        }
        .name {
          font-weight: 500;
          font-size: 24rpx;
          color: #333333;
        }

        .name,
        .label,
        .role {
          margin-top: 10rpx;
        }
      }
    }
  }
  .time-list-head {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 20rpx;
    .item {
      font-weight: 400;
      font-size: 24rpx;
      color: #666666;
      text-align: center;
    }
    .on {
      color: #c9a063;
    }
  }
  .time-list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    column-gap: 24rpx;
    row-gap: 20rpx;
    margin-top: 30rpx;
    .item {
      width: 100%;
      border: 1rpx solid #eee;
      border-radius: 8rpx;
      text-align: center;
      padding: 10rpx 0;
      position: relative;

      &.on {
        background-color: #d8b488;
        border-color: #d8b488;
        color: #ffffff;
      }
      &.disabled {
        background-color: #fff;
        color: #cccccc;
        border-color: #f0f0f0;
        position: relative;
        .time {
          color: #cccccc;
        }
        .state {
          width: 46rpx;
          height: 22rpx;
          background: #cccccc;
          border-radius: 0rpx 11rpx 0rpx 11rpx;
          font-weight: 400;
          font-size: 18rpx;
          color: #ffffff;
          position: absolute;
          top: 0;
          right: 0;
        }
      }
      .time {
        font-weight: 400;
        font-size: 22rpx;
      }
    }
  }
}
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 20rpx 24rpx;
  display: flex;
  align-items: center;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 20rpx);
  padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
  border-top: 1rpx solid #f0f0f0;
  .item {
    text-align: center;
    color: #666666;
    margin-right: 50rpx;
    .icon {
      width: 42rpx;
      height: 42rpx;
    }
    .text {
      font-weight: 400;
      font-size: 20rpx;
      color: #999999;
      margin-top: 6rpx;
    }
  }
  .btn {
    width: 428rpx;
    height: 78rpx;
    background: #c9a063;
    border-radius: 39rpx;
    font-weight: 400;
    font-size: 30rpx;
    color: #ffffff;
  }
}
.rest-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
  .rest-img {
    width: 154rpx;
    height: 164rpx;
  }
}
</style>
