<template>
  <view class="rights-body">
    <jyf-parser :html="content" ref="article" :tag-style="tagStyle"></jyf-parser>
  </view>
</template>

<script>
import { getArticleDetails } from '@/api/api'
export default {
  data() {
    return {
      content: '',
      tagStyle: {
        img: 'width:100%;display:block;',
      },
    }
  },
  onLoad(options) {
    getArticleDetails(4).then((res) => {
      console.log('🚀 ~ getArticleDetails ~  res.data:', res.data)
      this.content = res.data.content.content.replace(/<br\/>/gi, '')
      console.log('res', res)
    })
  },
}
</script>

<style lang="scss" scoped>
.rights-body {
  padding: 30rpx;
  background-color: #000000;
  min-height: 100vh;
}
</style>
