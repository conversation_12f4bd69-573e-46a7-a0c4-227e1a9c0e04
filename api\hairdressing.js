import request from '@/utils/request.js'

/**
 * 门店列表【含距离】
 * @param {*} params
 * @returns
 */
export function getMerchantList(params) {
  return request.get('store/merchant/lst', params, {
    noAuth: true,
  })
}

/**
 * 发型师列表
 * @param {*} id 门店Id
 * @param {*} params
 * @returns
 */
export function getHairstylist(id, params) {
  return request.get(`service/merchant/${id}/list`, params, {
    noAuth: true,
  })
}

/**
 * 门店服务列表
 * @param {*} id
 * @param {*} params
 * @returns
 */
export function getStoreServicesList(id, params) {
  return request.get(`product/spu/merchant/${id}`, params, {
    noAuth: true,
  })
}
/**
 * 服务分类
 * @param {*} id
 * @param {*} params
 * @returns
 */
export function getServerCategorys(params) {
  return request.get(`server/categorys`, params, {
    noAuth: true,
  })
}

/**
 * 评价-商家列表
 * @param {*} id
 * @param {*} params
 * @returns
 */
export function getReplyList(id, params) {
  return request.get(`store/product/reply/mer/${id}`, params, {
    noAuth: true,
  })
}
/**
 * 评价-发型师
 * @param {*} id
 * @param {*} params
 * @returns
 */
export function getReplyServiceList(id, params) {
  return request.get(`store/product/reply/service/${id}`, params, {
    noAuth: true,
  })
}
/**
 * 作品-商家作品列表
 * @param {*} id
 * @param {*} params
 * @returns
 */
export function getWorksList(id, params) {
  return request.get(`service/works/mer_id/${id}`, params, {
    noAuth: true,
  })
}

/**
 * 会员等级列表
 * @param {*} params
 * @returns
 */
export function getLevelLst(params) {
  return request.get(`level/lst`, params)
}

/**
 * 预约-查询发型师排班
 * @param {*} params
 * @returns
 */
export function getAppointment(params) {
  return request.get(`service/appointment/service_id`, params, {
    noAuth: true,
  })
}
/**
 * 预约-新增预约
 * @param {*} params
 * @returns
 */
export function postAppointment(params) {
  return request.post(`service/appointment`, params)
}

/**
 * 作品-发型师作品列表
 * @param {*} params
 * @returns
 */
export function getWorksServiceList(id, params) {
  return request.get(`service/works/service/${id}`, params, {
    noAuth: true,
  })
}


/**
 * 预约-我的预约
 * @param {*} params
 * @returns
 */
export function getAppointmentList(params) {
  return request.get(`service/appointment/lst`, params)
}
