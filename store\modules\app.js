// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import { getUserInfo, memberInfo } from '@/api/user.js'
import { getMerchantList } from '@/api/hairdressing.js'
import { diyProductApi } from '@/api/store.js'
import { LOGIN_STATUS, UID } from '../../config/cache'
import Cache from '../../utils/cache'
import { USER_INFO } from '../../config/cache'

const state = {
  token: Cache.get(LOGIN_STATUS) || null,
  uuid: uni.getStorageSync('uuid') || '',
  merid: uni.getStorageSync('merid') || '', //新店铺ID
  backgroundColor: '#fff',
  userInfo: null,
  uid: Cache.get(UID) || null,
  globalData: uni.getStorageSync('GLOBAL_DATA') || {},
  homeActive: false,
  copyPwd: null,
  pageFooter: uni.getStorageSync('pageFoot') || {},
  keyColor: Cache.get('KEY_COLOR') || '_default',
  viewColor:
    Cache.get('VIEW_COLOR') ||
    '--view-theme: #E93323;--view-assist:#FF7612;--view-priceColor:#E93323;--view-bgColor:rgba(255, 118, 18,.1);--view-minorColor:rgba(233, 51, 35,.1);--view-bntColor11:#FDA923;--view-bntColor12:#FD6523;--view-bntColor21:#F11B09;--view-bntColor22:#F67A38;',
  cartNum: null,
  orderPayList: [],
  // 当前门店信息
  storeInfo: uni.getStorageSync('storeInfo') ? JSON.parse(uni.getStorageSync('storeInfo')) : {},
  // 商品详情可视化数据
  diyProduct: {
    navList: [0, 1, 2, 3, 4], // 顶部菜单内容
    openShare: 1, //是否开启分享
    pictureConfig: 0, //轮播图模式 0 固定方图 1 高度自适应
    swiperDot: 1, //是否展示轮播指示点
    showPrice: [0, 1], //是否显示付费会员价和等级会员
    isOpen: [0, 1, 2], //是否展示 0 原价 1 累计销量 2 库存
    shareConfig: [1, 2], //分享收藏按钮
    showSvip: 1, //是否展示付费会员卡片
    showRank: 1, // 是否展示 排行榜卡片
    showService: [0, 1, 2, 3], //服务区卡片 0 营销活动入口 1 sku选择 2 服务保障 3 参数
    showReply: 1, //是否展示评论区
    replyNum: 3, //评论数量
    showMatch: 1, //是否展示搭配购
    matchNum: 3, //搭配套餐数量
    showStore: 1, //是否显示店铺
    showRecommend: 1, //是否展示推荐商品
    recommendNum: 12, //推荐商品数量
    menuList: [0, 1, 2], //底部左侧菜单
    showCart: 1, //是否显示购物车
    showCommunity: 0, //是否显示种草
    communityNum: 3,
    swiperHeight: '750',
    shareList: [
      { label: '客服', value: 0, icon: 'icon-ic_customerservice' },
      { label: '收藏', value: 1, icon: 'icon-ic_star' },
      { label: '分享', value: 2, icon: 'icon-ic_transmit1' },
    ],
    serviceList: [
      { name: '请选择', label: '规格选择', value: 0, info: '蓝色，2件', props: 'specSelect' },
      { name: '运费', label: '运费说明', value: 1, info: '免运费', props: 'freight' },
      {
        name: '保障',
        label: '服务保障',
        value: 2,
        info: '假一赔四 极速退款 七天无理由退换',
        props: 'serviceGuarantee',
      },
      { name: '参数', label: '参数说明', value: 3, info: '品牌 型号...', props: 'parameter' },
    ],
    footerList: [
      { label: '店铺', value: 0, icon: 'icon-ic_mall' },
      { label: '客服', value: 1, icon: 'icon-ic_customerservice' },
      { label: '购物车', value: 2, icon: 'icon-ic_ShoppingCart' },
      { label: '收藏', value: 3, icon: 'icon-ic_star' },
      { label: '分享', value: 4, icon: 'icon-ic_transmit1' },
      { label: '首页', value: 5, icon: 'icon-ic_home' },
    ],
    // 会员信息
    memberInfo: {},
    // 最近的门店信息
    merchantInfo: {},
  },
}
const mutations = {
  LOGIN(state, opt) {
    state.token = opt.token
    Cache.set(LOGIN_STATUS, opt.token, opt.time)
    uni.removeStorageSync('auth_token')
  },
  SETUID(state, val) {
    state.uid = val
    Cache.set(UID, val)
  },
  SETUUID(state, val) {
    state.uuid = val
    uni.setStorageSync('uuid', val)
  },
  UPDATE_LOGIN(state, token) {
    state.token = token
  },
  LOGOUT(state) {
    state.token = null
    state.uid = null
    Cache.clear(LOGIN_STATUS)
    Cache.clear(UID)
  },
  BACKGROUND_COLOR(state, color) {
    state.color = color
    document.body.style.backgroundColor = color
  },
  UPDATE_USERINFO(state, userInfo) {
    userInfo.isNew && uni.setStorageSync('is_new_user', true)
    state.userInfo = userInfo
  },
  OPEN_HOME(state) {
    state.homeActive = true
  },
  CLOSE_HOME(state) {
    state.homeActive = false
  },
  PARSE_PWD(state, pwd) {
    state.copyPwd = pwd
  },
  VIEW_COLOR(state, color) {
    Cache.set('VIEW_COLOR', color)
    state.viewColor = color
  },
  KEY_COLOR(state, key) {
    Cache.set('KEY_COLOR', key)
    state.keyColor = key
  },
  GLOBAL_DATA(state, key) {
    uni.setStorageSync('GLOBAL_DATA', key)
    state.globalData = key
  },
  FOOT_UPLOAD(state, data) {
    state.pageFooter = data
  },
  UPDATE_CARTNUM(state, data) {
    state.cartNum = data
  },
  UPDATE_ORDERPAYLIST(state, data) {
    state.orderPayList = data
  },
  SET_PRODUCT_DIY(state, data) {
    state.diyProduct = data.value
  },
  SET_MEMBER_INFO(state, data) {
    state.memberInfo = data
  },
  // 设置门店信息
  SET_STORE_INFO(state, storeInfo) {
    state.storeInfo = storeInfo
    // 同时存储到本地存储
    uni.setStorageSync('storeInfo', JSON.stringify(storeInfo))
  },
  // 清除门店信息
  CLEAR_STORE_INFO(state) {
    state.storeInfo = {}
    uni.removeStorageSync('storeInfo')
  },
}

const actions = {
  USERINFO({ state, commit }, force) {
    if (state.userInfo !== null && !force) return Promise.resolve(state.userInfo)
    else
      return new Promise((reslove) => {
        getUserInfo().then((res) => {
          commit('UPDATE_USERINFO', res.data)
          Cache.set(USER_INFO, res.data)
          reslove(res.data)
        })
      }).catch(() => {})
  },
  // div商品详情
  async getDiyProduct({ commit }) {
    let result = await diyProductApi()
    if (result.status == 200) {
      commit('SET_PRODUCT_DIY', result.data)
    } else {
      commit('SET_PRODUCT_DIY', this.diyProduct)
    }
  },
  // 获取会员信息
  async getMemberInfo() {
    const result = await memberInfo()
    console.log('🚀 ~ getMemberInfo ~ result:', result)
    if (result.status == 200) {
      commit('SET_MEMBER_INFO', result.data)
    }
  },
  // 获取门店列表
  loadMerchantList() {
    
    uni.getLocation({
      type: 'wgs84', // 坐标类型
      success(res) {
        console.log('当前位置的经度：' + res.longitude)
        console.log('当前位置的纬度：' + res.latitude)
        getMerchantList({
          lng: res.longitude,
          lat: res.latitude,
        }).then((res) => {
          this.merchantInfo = res.data.list[0] || {}
          this.merid = this.merchantInfo.mer_id ? this.merchantInfo.mer_id : ''
          Cache.set('merid', this.merid)
          console.log('🚀 ~ success ~ this.merchantInfo:', this.merchantInfo)
          console.log('🚀 ~ success ~  this.merid:', this.merid)
        })
      },
    })
  },
}

export default {
  state,
  mutations,
  actions,
}
