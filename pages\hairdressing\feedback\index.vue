<template>
  <view class="feedback-page" :style="viewColor">
    <view class="header-bg">
      <view class="header">
        <view class="header-title">意见反馈</view>
        <view class="header-desc">Hi,给出您的建议吧～</view>
      </view>
      <image
        class="header-img"
        src="http://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/2e1a6202507150956343749.png"
        mode="widthFix"
      />
    </view>
    <view class="card">
      <!-- 反馈类型选择 -->
      <view class="type-group">
        <view class="type-title">服务反馈</view>
        <view class="type-row">
          <view
            v-for="item in typeList.service"
            :key="item"
            :class="['type-btn', feedbackType === item ? 'active' : '']"
            @tap="selectType(item)"
          >
            {{ item }}
          </view>
        </view>
        <view class="type-row">
          <view
            v-for="item in typeList.service2"
            :key="item"
            :class="['type-btn', feedbackType === item ? 'active' : '']"
            @tap="selectType(item)"
          >
            {{ item }}
          </view>
        </view>
        <view class="type-title">产品反馈</view>
        <view class="type-row">
          <view
            v-for="item in typeList.product"
            :key="item"
            :class="['type-btn', feedbackType === item ? 'active' : '']"
            @tap="selectType(item)"
          >
            {{ item }}
          </view>
        </view>
        <view class="type-title">门店反馈</view>
        <view class="type-row">
          <view
            v-for="item in typeList.store"
            :key="item"
            :class="['type-btn', feedbackType === item ? 'active' : '']"
            @tap="selectType(item)"
          >
            {{ item }}
          </view>
        </view>
        <view class="type-title">其他反馈</view>
        <view class="type-row">
          <view
            v-for="item in typeList.other"
            :key="item"
            :class="['type-btn', feedbackType === item ? 'active' : '']"
            @tap="selectType(item)"
          >
            {{ item }}
          </view>
        </view>
      </view>
    </view>
    <view class="card">
      <!-- 反馈内容 -->
      <view class="label required">反馈内容</view>
      <view class="textarea-wrap">
        <textarea
          v-model="content"
          maxlength="1000"
          placeholder="请描述您要反馈的内容"
          class="textarea"
        />
        <view class="count">{{ content.length }}/1000</view>
      </view>
      <!-- 上传图片 -->
      <view class="label">上传图片</view>
      <view class="img-upload">
        <view class="img-list">
          <image
            v-for="(img, idx) in images"
            :key="idx"
            :src="img"
            class="img-thumb"
            mode="aspectFill"
          />
          <view v-if="images.length < 3" class="img-add" @tap="chooseImage">
            <image src="/static/images/icon-upload.png" class="img-add-icon" />
          </view>
        </view>
      </view>
    </view>
    <view class="card">
      <!-- 联系电话 -->
      <view class="label required">联系电话</view>
      <input v-model="contact" class="input" placeholder="请输入您的手机号码" maxlength="30" />
      <!-- 匿名提交 -->
      <view class="anonymous-row">
        <label class="label">
          <checkbox value="ss" class="round" />
          <text>匿名提交</text>
        </label>
      </view>

      <!-- 提交按钮 -->
    </view>
    <button class="submit-btn center" hover-class="button-hover" @tap="submit">提交反馈</button>
  </view>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  data() {
    return {
      content: '',
      images: [],
      contact: '',
      anonymous: true,
      feedbackType: '剪发技术',
      typeList: {
        service: ['剪发技术', '服务态度', '违规操作'],
        service2: ['发型调整'],
        product: ['优化建议', '产品建议'],
        store: ['门店卫生', '物品遗落'],
        other: ['其他'],
      },
    }
  },
  computed: {
    ...mapGetters(['viewColor']),
  },
  methods: {
    selectType(type) {
      this.feedbackType = type
    },
    chooseImage() {
      uni.chooseImage({
        count: 3 - this.images.length,
        success: (res) => {
          this.images = this.images.concat(res.tempFilePaths).slice(0, 3)
        },
      })
    },
    submit() {
      if (!this.content.trim()) {
        uni.showToast({ title: '请填写反馈内容', icon: 'none' })
        return
      }
      if (!this.contact.trim()) {
        uni.showToast({ title: '请填写联系电话', icon: 'none' })
        return
      }
      // 提交逻辑
      uni.showToast({ title: '提交成功', icon: 'success' })
      this.content = ''
      this.images = []
      this.contact = ''
      this.feedbackType = '剪发技术'
      this.anonymous = true
    },
  },
}
</script>
<style>
/*每个页面公共css */
/* //设置圆角 */
checkbox.round .wx-checkbox-input,
checkbox.round .uni-checkbox-input {
  width: 38rpx;
  height: 38rpx;
  border-radius: 50%;
  border: 2px solid #c9a063;
}

/* //设置背景色 */
/* checkbox.red[checked] .wx-checkbox-input, 
checkbox.red.checked .uni-checkbox-input {
  background-color: #ffca28 !important;
  border-color: #ffca28 !important;
  color: #ffffff !important;
} */
checkbox .uni-checkbox-input.uni-checkbox-input-checked,
checkbox .wx-checkbox-input.wx-checkbox-input-checked {
  border: 1px solid #c9a063 !important;
  background-color: #c9a063 !important;
  color: #fff !important;
}

uni-checkbox .uni-checkbox-input.uni-checkbox-input-checked:before {
  content: '\2022';
}
</style>

<style lang="scss" scoped>
.feedback-page {
  background: #f5f6fa;
  min-height: 100vh;
  padding: 0;
}
.header-bg {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(0deg, #f2f3f7 0%, #fff9f1 35%, #deb782 100%);
  position: relative;
}
.header {
  padding: 48rpx 32rpx 0 32rpx;
}
.header-title {
  font-weight: 600;
  font-size: 40rpx;
  color: #333333;
  width: fit-content;
  padding-bottom: 10rpx;
  background: url('http://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/22cb6202506231642544308.png')
    no-repeat;
  background-size: 100% 34rpx;
  background-position: bottom center;
}
.header-desc {
  font-weight: 400;
  font-size: 24rpx;
  color: #333333;
  margin-top: 8rpx;
}
.header-img {
  width: 228rpx;
  height: 143rpx;
  margin: 42rpx 82rpx 0 0;
}
.card {
  margin: 0 30rpx 10rpx;
  background: #fff;
  border-radius: 32rpx;
  padding: 38rpx 20rpx;
}
.type-group {
  margin-bottom: 32rpx;
}
.type-title {
  font-weight: 500;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 20rpx;
}
.type-row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  flex-wrap: wrap;
  gap: 44rpx 20rpx;
  margin-bottom: 40rpx;
}
.type-btn {
  background: #f5f5f5;
  border-radius: 30rpx;
  font-weight: 400;
  font-size: 26rpx;
  color: #333333;
  padding: 16rpx 42rpx;
}
.type-btn.active {
  background: #c49e62;
  color: #fff;
  font-weight: bold;
}
.label {
  font-weight: 500;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 20rpx;
}
.required::before {
  content: '*';
  color: #c49e62;
  margin-right: 4rpx;
}
.textarea-wrap {
  position: relative;
}
.textarea {
  min-height: 160rpx;
  border: none;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 16px;
  background: #f5f6fa;
  margin-bottom: 8rpx;
  resize: none;
  margin-bottom: 40rpx;
}
.count {
  position: absolute;
  right: 20rpx;
  bottom: 16rpx;
  font-weight: 400;
  font-size: 26rpx;
  color: #333333;
}
.img-upload {
  margin-bottom: 24rpx;
}
.img-list {
  display: flex;
  gap: 16rpx;
  margin-bottom: 8rpx;
}
.img-thumb {
  width: 120rpx;
  height: 120rpx;
  border-radius: 16rpx;
  object-fit: cover;
  border: 1px solid #e5e5e5;
}
.img-add {
  width: 148rpx;
  height: 148rpx;
  border-radius: 16rpx;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}
.img-add-icon {
  width: 48rpx;
  height: 42rpx;
}
.input {
  height: 80rpx;
  border: none;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 16px;
  background: #f5f6fa;
  margin-bottom: 32rpx;
  margin-top: 8rpx;
}

.submit-btn {
  height: 98rpx;
  background: #c9a063;
  color: #fff;
  border: none;
  border-radius: 50rpx;
  margin: 40rpx 30rpx;
  font-weight: bold;
  letter-spacing: 2rpx;
  font-weight: 400;
  font-size: 30rpx;
  color: #f5f5f5;
}
.anonymous-row {
  .label {
    display: flex;
    align-items: center;
  }
}
</style>
