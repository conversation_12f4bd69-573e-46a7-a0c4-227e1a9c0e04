<template>
  <view class="works-list">
    <!-- 分类Tabs -->
    <!-- <view class="works-list__tabs">
      <text class="works-list__tab works-list__tab--active">全部</text>
      <text class="works-list__tab">洗剪吹</text>
      <text class="works-list__tab">烫染</text>
      <text class="works-list__tab">洗吹</text>
      <text class="works-list__tab">护理</text>
    </view> -->
    <!-- 作品列表 -->
    <view class="works-list__container">
      <view class="works-list__grid">
        <!-- 卡片1 -->
        <block v-for="works in worksList" :key="works.id">
          <view class="works-list__card" @click="toDetails(works)">
            <view class="works-list__card-img-wrap">
              <image class="works-list__card-img" :src="works.files[0]" mode="aspectFill" />
              <view class="works-list__card-labels">
                <block v-for="(tag, idx) in works.tags" :key="idx">
                  <text class="works-list__card-label">{{ tag }}</text>
                </block>
              </view>
            </view>
            <view class="works-list__card-info">
              <view class="works-list__card-title">{{ works.title }}</view>
              <view class="works-list__card-stylist">
                <image
                  class="works-list__card-avatar"
                  :src="works.storeService.avatar"
                  mode="widthFix"
                />
                <text class="works-list__card-stylist-name">{{ works.storeService.nickname }}</text>
                <text class="works-list__card-stylist-role">
                  {{ works.storeService.level_name }}
                </text>
              </view>
            </view>
          </view>
        </block>

        <!-- 卡片2 -->
        <view class="works-list__card">
          <view class="works-list__card-img-wrap">
            <image
              class="works-list__card-img"
              src="https://dummyimage.com/320x420/cccccc/ffffff&text=2"
              mode="aspectFill"
            />
            <view class="works-list__card-labels">
              <text class="works-list__card-label">修饰脸型</text>
              <text class="works-list__card-label">减龄</text>
            </view>
            <view class="works-list__card-play">
              <text class="iconfont icon-ic_right2"></text>
            </view>
          </view>
          <view class="works-list__card-info">
            <view class="works-list__card-title">韩系三七分</view>
            <view class="works-list__card-stylist">
              <image
                class="works-list__card-avatar"
                src="https://dummyimage.com/40x40/cccccc/ffffff&text=L"
                mode="aspectFill"
              />
              <text class="works-list__card-stylist-name">李老师</text>
              <text class="works-list__card-stylist-role">总监</text>
            </view>
          </view>
        </view>
        <!-- 卡片3 -->
        <view class="works-list__card">
          <view class="works-list__card-img-wrap">
            <image
              class="works-list__card-img"
              src="https://dummyimage.com/320x420/cccccc/ffffff&text=1"
              mode="aspectFill"
            />
            <view class="works-list__card-labels">
              <text class="works-list__card-label">修饰脸型</text>
              <text class="works-list__card-label">减龄</text>
            </view>
          </view>
          <view class="works-list__card-info">
            <view class="works-list__card-title">甜美大波浪</view>
            <view class="works-list__card-stylist">
              <image
                class="works-list__card-avatar"
                src="https://dummyimage.com/40x40/cccccc/ffffff&text=Z"
                mode="aspectFill"
              />
              <text class="works-list__card-stylist-name">郑老师</text>
              <text class="works-list__card-stylist-role">店长</text>
            </view>
          </view>
        </view>
        <!-- 卡片4 -->
        <view class="works-list__card">
          <view class="works-list__card-img-wrap">
            <image
              class="works-list__card-img"
              src="https://dummyimage.com/320x420/cccccc/ffffff&text=2"
              mode="aspectFill"
            />
            <view class="works-list__card-labels">
              <text class="works-list__card-label">修饰脸型</text>
              <text class="works-list__card-label">减龄</text>
            </view>
            <view class="works-list__card-play">
              <text class="iconfont icon-ic_right2"></text>
            </view>
          </view>
          <view class="works-list__card-info">
            <view class="works-list__card-title">韩系三七分</view>
            <view class="works-list__card-stylist">
              <image
                class="works-list__card-avatar"
                src="https://dummyimage.com/40x40/cccccc/ffffff&text=L"
                mode="aspectFill"
              />
              <text class="works-list__card-stylist-name">李老师</text>
              <text class="works-list__card-stylist-role">总监</text>
            </view>
          </view>
        </view>
      </view>
      <view class="emp">{{ loadTitle }}</view>
    </view>
  </view>
</template>

<script>
import { getWorksList } from '@/api/hairdressing.js'
export default {
  name: 'WorksList',
  data() {
    return {
      mer_id: '',
      worksList: [],
      page: 1,
      limit: 10,
      loading: false,
      loadend: false,
      loadTitle: '加载更多',
    }
  },
  onLoad(options) {
    if (options.mer_id) {
      this.mer_id = options.mer_id
      this.loadWorksList()
    }
  },
  methods: {
    loadWorksList() {
      if (this.loading || this.loadend) return
      this.loading = true
      this.loadTitle = ''

      getWorksList(this.mer_id, {
        page: this.page,
        limit: this.limit,
      })
        .then((res) => {
          let list = res.data.list
          let loadend = list.length < this.limit

          this.worksList = this.worksList.concat(list)
          this.loadend = loadend
          this.loading = false
          this.loadTitle = loadend ? '已全部加载' : '加载更多'
          this.page += 1
        })
        .catch((err) => {
          this.loading = false
          this.loadTitle = '加载更多'
        })
    },
    toDetails(works) {
      uni.navigateTo({
        url: '/pages/hairdressing/work_details/index?info=' + JSON.stringify(works),
      })
    },
  },
  onReachBottom() {
    this.loadWorksList()
  },
}
</script>

<style lang="scss" scoped>
.works-list {
  min-height: 100vh;
  padding: 30rpx;

  &__tabs {
    display: flex;
    align-items: center;
    margin-bottom: 40rpx;
    column-gap: 52rpx;
  }
  &__tab {
    font-size: 28rpx;
    color: #999;
    position: relative;
    font-weight: 400;
    z-index: 9;
    &--active {
      color: #333;
      font-size: 32rpx;
      font-weight: 600;
      &::after {
        content: '';
        display: block;
        width: 100%;
        height: 10rpx;
        border-radius: 6rpx;
        background: linear-gradient(-90deg, #ffffff, #d19e58);
        position: absolute;
        left: 0;
        bottom: 0;
        z-index: -1;
      }
    }
  }

  &__container {
  }
  &__grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 22rpx 20rpx;
  }
  &__card {
    width: 100%;
    background: #fff;
    border-radius: 12rpx;
    box-shadow: 0 6rpx 36rpx 0 #f6f6f6;
  }
  &__card-img-wrap {
    position: relative;
    width: 100%;
    height: 400rpx;
  }
  &__card-img {
    width: 100%;
    height: 100%;
    border-radius: 12rpx;
  }
  &__card-labels {
    position: absolute;
    left: 20rpx;
    bottom: 20rpx;
    display: flex;
    column-gap: 10rpx;
  }
  &__card-label {
    background: rgba(0, 0, 0, 0.6);
    color: #fff;
    font-size: 26rpx;
    border-radius: 8rpx;
    padding: 8rpx 18rpx;
  }
  &__card-play {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    width: 46rpx;
    height: 46rpx;
    background: rgba(0, 0, 0, 0.12);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    .iconfont {
      font-size: 28rpx;
      color: white;
    }
  }
  &__card-info {
    padding: 20rpx;
  }
  &__card-title {
    font-size: 28rpx;
    color: #333333;
    font-weight: 500;
    margin-bottom: 10rpx;
  }
  &__card-stylist {
    display: flex;
    align-items: center;
    gap: 10rpx;
  }
  &__card-avatar {
    width: 48rpx;
    height: 48rpx;
    border-radius: 50%;
  }
  &__card-stylist-name {
    font-size: 24rpx;
    color: #333;
  }
  &__card-stylist-role {
    font-size: 22rpx;
    color: #999999;
    display: flex;
    align-items: center;
    column-gap: 8rpx;
    &:before {
      content: ' ';
      display: block;
      width: 1rpx;
      height: 18rpx;
      background: #e6e6e6;
    }
  }
}
.emp {
  text-align: center;
  color: #ccc;
  padding: 30rpx 0;
}
</style>
