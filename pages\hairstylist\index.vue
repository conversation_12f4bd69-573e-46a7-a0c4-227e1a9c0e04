<template>
  <view class="hairstylist-page">
    <view class="store-list" v-if="storeList.length > 0">
      <block v-for="item in storeList" :key="item.mer_id">
        <view class="store-item">
          <view class="store-header">
            <view class="store-info">
              <view class="store-name-status" @click="handleStore(item)">
                <text class="name">{{ item.mer_name }}</text>
                <text class="iconfont icon-ic_rightarrow"></text>
                <view class="status center">
                  <text>营业中</text>
                </view>
                <text class="time">{{ item.mer_take_time.join('-') }}</text>
              </view>
              <view class="store-address">
                <text class="iconfont icon-ic_location51"></text>
                <text class="address-text">{{ item.mer_address }}</text>
                <text class="iconfont icon-icon_copy" @click="handleCopy(item.mer_address)"></text>
              </view>
            </view>
            <view class="store-distance">
              <text>距您{{ item.distance }}</text>
            </view>
          </view>
          <view class="hairstylist-list">
            <view class="hairstylist-item" v-for="stylist in item.hairstylists" :key="stylist.service_id">
              <view class="stylist-main">
                <image class="avatar" :src="stylist.avatar" mode="aspectFill"></image>
                <view class="stylist-content">
                  <view class="stylist-info">
                    <view class="stylist-name-title">
                      <text class="name">{{ stylist.nickname }}</text>
                      <RoleTag :role="stylist.level_name" />
                      <view class="rating">
                        <text class="rating-label">满意值</text>
                        <text class="rating-rate">{{ stylist.manyizhi }}</text>
                      </view>
                    </view>
                    <view class="stylist-services">
                      <view
                        class="service-item"
                        v-for="service in stylist.product"
                        :key="service.id"
                      >
                        <view class="price-info">
                          <view class="price">
                            <text>¥</text>
                            {{ service.product.price }}
                          </view>
                          <view class="discount" v-if="service.discount">
                            {{ service.discount }}折
                          </view>
                          <view class="original-price">¥{{ service.product.ot_price }}</view>
                        </view>
                        <text class="service-name">
                          {{ service.product.store_name }}
                        </text>
                      </view>
                    </view>
                  </view>
                </view>
                <view class="appointment-btn-wrapper">
                  <button
                    class="appointment-btn"
                    hover-class="btnHoverClass"
                    @click="handleAppointment(item, stylist)"
                  >
                    去预约
                  </button>
                </view>
              </view>
              <view class="stylist-actions">
                <view class="action-btn" @click="openSchedulingPopup(stylist)">
                  <image
                    src="/static/images/icon-paiban.png"
                    class="action-btn-icon"
                    mode="widthFix"
                  />
                  <text>排班</text>
                </view>
                <view class="action-btn">
                  <image
                    src="/static/images/icon-meifa.png"
                    class="action-btn-icon"
                    mode="widthFix"
                  />
                  <text>作品</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </block>
    </view>
    <!-- 排班 -->
    <Scheduling ref="schedulingRef" />
    <!--自定义底部tab栏-->
    <customTab :newData="newData" :activeRouter="activeRouter"></customTab>
  </view>
</template>

<script>
import customTab from '@/components/customTab'
import { getNavigation } from '@/api/public.js'
import RoleTag from '@/components/roleTags/roleTags.vue'
import Scheduling from '@/components/scheduling/scheduling'
import { getMerchantList, getHairstylist, getStoreServicesList } from '@/api/hairdressing.js'

export default {
  components: { customTab, RoleTag, Scheduling },
  data() {
    return {
      newData: {},
      activeRouter: '',
      storeList: [],
      params: {
        page: 1,
        limit: 10,
        latitude: '',
        longitude: '',
      },
      loading: false,
      loadend: false,
    }
  },
  onLoad(options) {
    this.getLocation()
  },
  onShow() {
    let routes = getCurrentPages()
    let curRoute = routes[routes.length - 1].route
    this.activeRouter = '/' + curRoute
    this.getNav()
  },
  methods: {
    getLocation() {
      uni.authorize({
        scope: 'scope.userLocation',
        success: () => {
          uni.getLocation({
            type: 'gcj02',
            success: (res) => {
              this.params.longitude = res.longitude
              this.params.latitude = res.latitude
            },
            complete: () => {
              this.loadMerchantList()
            },
          })
        },
        fail: (err) => {
          this.loadMerchantList()
        },
      })
    },
    // 获取门店列表
    loadMerchantList() {
      const params = {
        lng: this.params.longitude,
        lat: this.params.latitude,
      }
      getMerchantList(params).then((res) => {
        //截取前俩个
        // this.storeList = res.data.list.slice(0, 2)
        const storeList = res.data.list.filter((item) => item.mer_id === 2 || item.mer_id === 6)
        this.storeList = storeList
        this.storeList.forEach((item) => {
          this.loadHairstylist(item)
        })
      })
    },

    loadHairstylist(item) {
      getHairstylist(item.mer_id).then((res) => {
        console.log('🚀 ~ getHairstylist ~ res:', res.data.list.slice(0, 2))
        const hairstylist = res.data.list.slice(0, 2)
        hairstylist.forEach((stylist) => {
          this.loadStoreServicesList(item.mer_id, stylist)
        })
        this.$set(item, 'hairstylists', res.data.list.slice(0, 2))
      })
    },
    loadStoreServicesList(mer_id, item) {
      getStoreServicesList(mer_id, {
        service_level: item.level_name,
        is_good: 1,
      }).then((res) => {
        res.data.list.forEach((service) => {
          //根据原价 price和现价 ot_price 算折扣
          if (service.product.ot_price && service.product.price && !service.discount) {
            service.discount = ((service.product.price / service.product.ot_price) * 10).toFixed(1)
          }
          //判断价格是否有小数，并且小数点后大于0
          service.product.price = Number(service.product.price)
          service.product.ot_price = Number(service.product.ot_price)
        })
        this.$set(item, 'product', res.data.list)
      })
    },
    getNav() {
      getNavigation().then((res) => {
        this.newData = res.data
        if (this.newData.status && this.newData.status.status) {
          uni.hideTabBar()
        } else {
          uni.showTabBar()
        }
      })
    },
    // 预约
    handleAppointment(item, stylist) {
      console.log("🚀 ~ handleAppointment ~ stylist:", stylist)
      uni.navigateTo({
        url: `/pages/hairdressing/appointment/index?info=${JSON.stringify(item)}&service_id=${
          stylist.service_id
        }`,
        // url: `/pages/hairdressing/place_order/index?info=${JSON.stringify(item)}`,
      })
    },
    // 跳转门店详情
    handleStore(store) {
      uni.navigateTo({
        url: `/pages/hairdressing/store/index?store=${JSON.stringify(store)}`,
      })
    },
    openSchedulingPopup(stylist) {
      this.$refs.schedulingRef.open(stylist)
    },
    //处理复制
    handleCopy(text) {
      uni.setClipboardData({
        data: text,
        success() {
          uni.showToast({
            title: '复制成功',
            icon: 'success',
          })
        },
        fail(err) {
          console.log('🚀 ~ fail ~ err:', err)
          uni.showToast({
            title: '复制失败',
            icon: 'none',
          })
        },
      })
    },
  },
  onPullDownRefresh() {
    this.getNav()
    this.getLocation()
    uni.stopPullDownRefresh()
  },
  onReachBottom() {},
}
</script>

<style lang="scss" scoped>
.hairstylist-page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.store-list {
  padding: 30rpx;
}

.store-item {
  background-color: #fff;
  border-radius: 30rpx;
  // padding: 28rpx 26rpx;
  margin-bottom: 10rpx;
}

.store-header {
  display: flex;
  justify-content: space-between;
  background: linear-gradient(90deg, #ffffff, #ffffff, #fffaf2);
  border-radius: 30rpx 30rpx 0 0;
  padding: 28rpx 26rpx 20rpx;
}

.store-info .store-name-status {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;

  .name {
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
  }
  .iconfont {
    font-size: 28rpx;
    color: #999;
    font-weight: bold;
    margin-left: 10rpx;
  }

  .status {
    width: 76rpx;
    height: 28rpx;
    background: linear-gradient(0deg, #2f2c24, #755e18);
    border-radius: 8rpx;
    font-weight: 400;
    font-size: 20rpx;
    color: #ffffff;
    margin-left: 20rpx;
  }
  .time {
    margin-left: 10rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #999999;
  }
}

.store-address {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999999;

  .iconfont {
    font-size: 28rpx;
  }

  .address-text {
    max-width: 450rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-weight: 400;
    font-size: 20rpx;
    color: #adadad;
    margin: 0 10rpx;
  }

  .icon-fuzhi {
    margin-left: 10rpx;
  }
}

.store-distance {
  font-size: 24rpx;
  color: #c9a063;
}
.hairstylist-list {
  padding: 0 20rpx;
  .hairstylist-item {
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f2f3f7;

    &:last-child {
      border-bottom: none;
    }
  }

  .stylist-main {
    display: flex;
    align-items: stretch;
  }

  .avatar {
    width: 98rpx;
    height: 98rpx;
    border-radius: 50%;
    margin-right: 20rpx;
    flex-shrink: 0;
  }

  .stylist-content {
    flex: 1;
    display: flex;
    min-width: 0;
  }

  .stylist-info {
    flex: 1;
    min-width: 0;
  }

  .stylist-name-title {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 10rpx;

    .name {
      font-size: 28rpx;
      font-weight: bold;
      color: #333;
    }

    .title {
      width: 68rpx;
      height: 28rpx;
      background: linear-gradient(48deg, #ffffff, #434343, #7c7c7c, #434343);
      border-radius: 6rpx;
      font-weight: 500;
      font-size: 18rpx;
      color: #fff4e0;
      margin: 0 10rpx;
    }

    .rating {
      width: 98rpx;
      height: 28rpx;
      background: linear-gradient(-34deg, #ffffff, #f8eedc, #c9a063);
      border-radius: 6rpx;
      font-weight: 300;
      font-size: 18rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 6rpx;
      color: #a27630;
      &-label {
        font-weight: 300;
      }
    }
  }

  .stylist-services .service-item {
    display: flex;
    align-items: center;
    margin-bottom: 10rpx;

    .price-info {
      display: flex;
      align-items: center;
      flex-wrap: nowrap;
      white-space: nowrap;

      .price {
        font-size: 32rpx;
        font-weight: bold;
        color: #a01c1c;

        text {
          font-size: 22rpx;
        }
      }

      .discount {
        font-size: 20rpx;
        color: #a01c1c;
        border-radius: 4rpx;
        padding: 2rpx 6rpx;
        margin: 0 10rpx;
        background: #f7e9e9;
        border-radius: 3rpx;
        position: relative;
        &::before {
          content: '';
          position: absolute;
          left: -6rpx;
          top: 50%;
          transform: translateY(-50%);
          width: 0;
          height: 0;
          border-style: solid;
          border-width: 8rpx 8rpx 8rpx 0;
          border-color: transparent #f7e9e9 transparent transparent;
        }
      }

      .original-price {
        font-size: 22rpx;
        color: #cccccc;
        text-decoration: line-through;
      }
    }

    .service-name {
      font-size: 26rpx;
      color: #333;
      margin-left: 20rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      //超过一定长度显示省略号
    }
  }

  .appointment-btn-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .appointment-btn {
    background-color: #d5a162;
    color: #fff;
    padding: 15rpx 30rpx;
    border-radius: 30rpx;
    font-size: 26rpx;
    text-align: center;
    align-self: center;
    margin-left: 20rpx;
    flex-shrink: 0;
  }

  .stylist-actions {
    display: flex;
    align-items: center;
    margin-top: 18rpx;
    padding-left: 120rpx;
    .action-btn {
      display: flex;
      align-items: center;
      background-color: #f0f0f0;
      padding: 5rpx 15rpx;
      border-radius: 6rpx;
      font-size: 22rpx;
      color: #999;
      margin-right: 15rpx;
      .action-btn-icon {
        width: 28rpx;
        height: 28rpx;
        margin-right: 10rpx;
      }
    }
  }
}
</style>
