<template>
  <view class="expression">
    <text>{{ expression }}</text>
    <image :src="icon" mode="widthFix" class="icon"></image>
  </view>
</template>

<script>
export default {
  name: 'expression',
  props: {
    score: {
      type: Number,
      default: 5,
    },
  },
  computed: {
    expression() {
      if (this.score >= 3) {
        return '满意'
      } else if (this.score >= 1) {
        return '一般'
      } else {
        return '不满意'
      }
    },
    icon() {
      if (this.score >= 3) {
        return '/static/images/icon-expression-1.png'
      } else if (this.score >= 1) {
        return '/static/images/icon-expression-2.png'
      } else {
        return '/static/images/icon-expression-5.png'
      }
    },
  },

  data() {
    return {}
  },
}
</script>

<style lang="scss" scoped>
.expression {
  font-weight: 400;
  font-size: 24rpx;
  color: #999999;
  display: flex;
  align-items: center;
  column-gap: 8rpx;
}
.icon {
  width: 42rpx;
  height: 42rpx;
}
</style>
