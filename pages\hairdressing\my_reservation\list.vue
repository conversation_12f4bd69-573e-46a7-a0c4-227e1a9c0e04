<template>
  <view class="reservation-page">
    <!-- 状态筛选标签 -->
    <view class="status-tabs">
      <scroll-view class="tab-scroll" scroll-x="true" show-scrollbar="false">
        <view class="tab-list">
          <view
            v-for="(tab, index) in statusTabs"
            :key="index"
            class="tab-item"
            :class="{ active: currentStatus === tab.value }"
            @click="changeStatus(tab.value)"
          >
            {{ tab.label }}
          </view>
        </view>
      </scroll-view>
    </view>
    <!-- 预约列表 -->
    <view class="list-container">
      <block v-for="(item, index) in listData" :key="index">
        <view class="reservation-item">
          <!-- 店铺信息 -->
          <view class="store-header">
            <view class="store-info">
              <image :src="item.merchant.mer_avatar" class="store-icon" mode="aspectFill" />
            </view>
            <!-- 预约信息 -->
            <view class="appointment-info">
              <view class="store-name">
                {{ item.merchant.mer_name }}
                <text class="iconfont icon-ic_rightarrow"></text>
              </view>
              <view class="info-row">
                <text class="label">预约时间：</text>
                <text class="value">{{ item.appointment_time || '2025-05-08' }}</text>
              </view>
              <view class="info-row">
                <text class="label">预约发型师：</text>
                <text class="value">{{ item.stylist_name || '到店分配' }}</text>
              </view>
              <view class="info-row">
                <text class="label">服务项目：</text>
                <text class="value">{{ item.service_name || '蛋白矫正' }}</text>
              </view>
            </view>
            <view class="status-tag">
              <!-- {{ (item.status) }} -->
            </view>
          </view>
          <!-- 操作按钮 -->
          <view class="action-buttons">
            <button
              hover-class="button-hover"
              class="btn btn-outline"
              @click="cancelAppointment(item)"
            >
              取消预约
            </button>
            <button
              hover-class="button-hover"
              class="btn btn-primary"
              @click="modifyAppointment(item)"
            >
              修改预约
            </button>
            <button hover-class="button-hover" class="btn btn-outline" @click="viewDetails(item)">
              查看预约
            </button>
          </view>
        </view>
      </block>
    </view>

    <!-- 加载更多提示 -->
    <view class="load-more" v-if="listData.length > 0">
      <text class="load-text">{{ loadTitle }}</text>
    </view>

    <!-- 空状态 -->
    <view v-if="!loading && listData.length === 0" class="empty-state">
      <image src="/static/images/no-order.png" class="empty-icon" mode="aspectFit" />
      <text class="empty-text">暂无预约记录</text>
    </view>
  </view>
</template>

<script>
import { getAppointmentList } from '@/api/hairdressing.js'
import myNavbar from '@/components/my-navbar/my-navbar.vue'

export default {
  components: {
    myNavbar,
  },
  data() {
    return {
      listData: [], // 预约列表数据
      count: 0, // 总数量
      loading: false, // 加载状态
      loadend: false, // 是否加载完毕
      loadTitle: '加载更多', // 加载提示文字
      currentStatus: 0, // 当前选中的状态
      pageData: {
        page: 1,
        limit: 10,
        status: 0, // 状态筛选
      },
      // 状态标签配置
      statusTabs: [
        { label: '待服务', value: 0 },
        { label: '已完成', value: 1 },
      ],
    }
  },
  onLoad() {
    this.getListData(true)
    // 添加一些模拟数据用于展示
  },
  methods: {
    /**
     * 获取预约列表数据
     * @param {Boolean} isRefresh 是否刷新数据（重置页码）
     */
    getListData(isRefresh = false) {
      // 防止重复请求
      if (this.loading || this.loadend) return
      // 如果是刷新，重置数据
      if (isRefresh) {
        this.pageData.page = 1
        this.listData = []
        this.loadend = false
      }
      this.loading = true
      this.loadTitle = '加载中...'
      // 添加状态筛选参数
      const params = { ...this.pageData }
      params.status = this.currentStatus
      getAppointmentList(params)
        .then((res) => {
          const list = res.data.result || []
          // 判断是否还有更多数据
          const loadend = list.length < this.pageData.limit
          // 更新数据
          this.count = res.data.count || 0
          this.listData = isRefresh ? list : this.listData.concat(list)
          this.loadend = loadend
          this.loading = false
          this.loadTitle = loadend ? '已全部加载' : '加载更多'
          // 页码递增
          this.pageData.page += 1
        })
        .catch((err) => {
          this.loading = false
          this.loadTitle = '加载更多'
          this.$util.Tips({ title: err.msg || '加载失败' })
        })
    },

    /**
     * 切换状态筛选
     */
    changeStatus(status) {
      this.currentStatus = status
      this.pageData.status = status
      this.getListData(true)
    },

    /**
     * 获取状态样式类
     */
    getStatusClass(status) {
      const classMap = {
        0: 'status-pending',
        1: 'status-processing',
        2: 'status-cancelled',
        3: 'status-completed',
      }
      return classMap[status] || ''
    },

    /**
     * 取消预约
     */
    cancelAppointment(item) {
      uni.showModal({
        title: '确认取消',
        content: '确定要取消这个预约吗？',
        success: (res) => {
          if (res.confirm) {
            // 调用取消预约API
            uni.showToast({
              title: '取消成功',
              icon: 'success',
            })
            this.getListData(true)
          }
        },
      })
    },

    /**
     * 修改预约
     */
    modifyAppointment(item) {
      uni.navigateTo({
        url: `/pages/hairdressing/appointment/index?id=${item.id}&mode=edit`,
      })
    },

    /**
     * 查看详情
     */
    viewDetails(item) {
      uni.navigateTo({
        url: `/pages/hairdressing/appointment_detail/index?id=${item.id}`,
      })
    },
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.getListData(true)
    setTimeout(() => {
      uni.stopPullDownRefresh()
    }, 1000)
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    if (this.listData.length > 0) {
      this.getListData()
    }
  },
}
</script>

<style lang="scss" scoped>
.reservation-page {
  background-color: #fafafa;
  min-height: 100vh;
}

// 导航栏右侧
.nav-right {
  .iconfont {
    font-size: 36rpx;
    color: #fff;
  }
}

// 状态筛选标签
.status-tabs {
  background-color: #fff;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;

  .tab-scroll {
    white-space: nowrap;
  }

  .tab-list {
    display: flex;
    padding: 0 30rpx;

    .tab-item {
      flex-shrink: 0;
      padding: 16rpx 32rpx;
      margin-right: 24rpx;
      font-size: 28rpx;
      color: #666;
      background-color: #f8f8f8;
      border-radius: 40rpx;
      transition: all 0.3s;

      &.active {
        color: #fff;
        background: linear-gradient(135deg, #d8b488 0%, #c19a5c 100%);
      }
    }
  }
}

// 列表容器
.list-container {
  padding: 20rpx 30rpx;
  .reservation-item {
    background-color: #fff;
    border-radius: 24rpx;
    margin-bottom: 20rpx;
    overflow: hidden;
    padding: 30rpx 20rpx;
  }
}

// 店铺头部
.store-header {
  display: flex;
  justify-content: space-between;
  .store-info {
    .store-icon {
      flex-shrink: 0;
      width: 100rpx;
      height: 100rpx;
      margin-right: 20rpx;
      border-radius: 12rpx;
    }
  }

  .status-tag {
    padding: 8rpx 20rpx;
    border-radius: 20rpx;
    font-size: 24rpx;
    font-weight: 500;

    &.status-pending {
      color: #d8b488;
      background-color: #fdf6e8;
    }

    &.status-processing {
      color: #1890ff;
      background-color: #e6f7ff;
    }

    &.status-cancelled {
      color: #999;
      background-color: #f5f5f5;
    }

    &.status-completed {
      color: #52c41a;
      background-color: #f6ffed;
    }
  }
}

// 预约信息
.appointment-info {
  flex: 1;
  .store-name {
    font-weight: 500;
    font-size: 28rpx;
    color: #333333;
    margin-bottom: 6rpx;
    display: flex;
    align-items: center;
    .iconfont {
      font-size: 20rpx;
      color: #999;
      margin-left: 8rpx;
      font-weight: bold;
    }
  }
  .info-row {
    display: flex;
    font-weight: 400;
    font-size: 24rpx;
    color: #999999;
    margin-bottom: 6rpx;
    .label {
      //   width: 140rpx;
      //   flex-shrink: 0;
    }
    .value {
      flex: 1;
    }
  }
}

// 操作按钮
.action-buttons {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 20rpx;
  .btn {
    padding: 12rpx 32rpx;
    border-radius: 40rpx;
    font-size: 26rpx;
    margin-left: 20rpx;
    text-align: center;

    &.btn-outline {
      color: #666;
      border: 1rpx solid #ddd;
      background-color: #fff;
    }

    &.btn-primary {
      color: #fff;
      background: linear-gradient(135deg, #d8b488 0%, #c19a5c 100%);
      border: none;
    }
  }
}

// 加载更多
.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;

  .load-text {
    font-size: 24rpx;
    color: #999;
  }
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 120rpx 0;

  .empty-icon {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 40rpx;
    opacity: 0.6;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}
</style>
