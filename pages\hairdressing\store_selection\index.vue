<template>
  <view class="new-store-page">
    <view class="new-store-body">
      <!-- 顶部栏：城市选择、搜索框、搜索按钮 -->
      <view class="top-bar">
        <view class="city-select" @click="onCitySelect">
          <text class="iconfont icon-ic_location1"></text>
          <text class="city-name">
            {{ currentCity }}
          </text>
          <text class="iconfont icon-ic_down2"></text>
        </view>
        <view class="search-box">
          <input v-model="searchKeyword" placeholder="搜索门店/项目/发型师" class="search-input" />
          <button class="search-btn center" hover-class="button-hover" @click="onSearch">
            <text class="iconfont icon-ic_search"></text>
          </button>
        </view>
      </view>
      <!-- 门店列表 -->
      <view class="store-list">
        <view class="store-card" v-for="(store, idx) in storeList" :key="idx">
          <view class="store-card-img">
            <image :src="store.img" class="store-img" mode="aspectFill" />
            <view v-if="store.common" class="tag-common center">当前选择</view>
          </view>
          <view class="store-info">
            <view class="store-header">
              <text class="store-name">{{ store.name }}</text>
              <text v-if="store.status === '营业中'" class="tag-open">营业中</text>
              <text v-else class="tag-close">休息中</text>
              <text class="store-time">9:00-21:30</text>
              <text class="store-near">最近</text>
            </view>
            <view class="store-address">
              <view class="store-address-left">
                <view>
                  {{ store.address }}
                  <text class="iconfont icon-icon_copy"></text>
                </view>
                <view class="distance">{{ store.distance }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="store-title">附近门店</view>
      <view class="store-list">
        <view class="store-card" v-for="(store, idx) in storeList" :key="idx">
          <view class="store-card-img">
            <image :src="store.img" class="store-img" mode="aspectFill" />
            <view v-if="store.common" class="tag-common center">当前选择</view>
          </view>
          <view class="store-info">
            <view class="store-header">
              <text class="store-name">{{ store.name }}</text>
              <text v-if="store.status === '营业中'" class="tag-open">营业中</text>
              <text v-else class="tag-close">休息中</text>
              <text class="store-time">9:00-21:30</text>
              <text class="store-near">最近</text>
            </view>
            <view class="store-address">
              <view class="store-address-left">
                <view>
                  {{ store.address }}
                  <text class="iconfont icon-icon_copy"></text>
                </view>
                <view class="distance">{{ store.distance }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      newData: {},
      currentCity: '上海',
      searchKeyword: '',
      storeList: [
        {
          img: 'https://dummyimage.com/128x128/3c9cff/fff',
          name: '春哥美发旗舰店',
          common: true,
          status: '营业中',
          score: 4.9,
          commentCount: 123,
          tags: ['环境好', '服务优', '技术棒'],
          projects: '剪发/烫发/染发/护理',
          address: '上海市浦东新区张江路88号',
          distance: '1.2km',
          product: [
            {
              id: 1,
              price: '226',
              ot_price: '604',
              store_name: '健康烫染',
              discount: 3.3
            },
            {
              id: 2,
              price: '37.9',
              ot_price: '68',
              store_name: '洗剪吹',
              discount: 5.5
            }
          ]
        },
        {
          img: 'https://dummyimage.com/128x128/3c9cff/fff',
          name: '春哥美发二店',
          common: false,
          status: '休息中',
          score: 4.7,
          commentCount: 56,
          tags: ['环境好', '价格实惠'],
          projects: '洗剪吹/造型设计',
          address: '上海市徐汇区漕溪北路100号',
          distance: '2.5km',
          product: [
            {
              id: 1,
              price: '226',
              ot_price: '604',
              store_name: '健康烫染',
              discount: 3.3
            },
            {
              id: 2,
              price: '37.9',
              ot_price: '68',
              store_name: '洗剪吹',
              discount: 5.5
            }
          ]
        }
      ]
    }
  },
  onShow() {},
  methods: {
    onCitySelect() {
      uni.showToast({ title: '城市选择功能待实现', icon: 'none' })
    },
    onSearch() {
      uni.showToast({ title: '搜索功能待实现', icon: 'none' })
    }
  }
}
</script>

<style lang="scss" scoped>
.new-store-page {
  background: #f7f7f7;
  min-height: 100vh;
  .new-store-body {
    padding: 30rpx;
  }

  .top-bar {
    display: flex;
    align-items: center;
    height: 68rpx;
    background: #ffffff;
    border-radius: 34rpx;
    margin-bottom: 20rpx;
    padding: 16rpx 0;

    .city-select {
      font-size: 28rpx;
      color: #333;
      margin-right: 24rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      padding-left: 40rpx;

      .iconfont {
        font-size: 24rpx;
      }
      .city-name {
        margin: 0 8rpx;
      }
    }

    .search-box {
      flex: 1;
      display: flex;
      align-items: center;
      border-left: 1px solid #f2f3f7;
      padding-left: 20rpx;
      height: 38rpx;

      .search-input {
        flex: 1;
        height: 56rpx;
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
      }

      .search-btn {
        width: 98rpx;
        height: 58rpx;
        background: #c9a063;
        border-radius: 29rpx;

        .iconfont {
          font-size: 34rpx;
          color: #fff;
        }
      }
    }
  }
  .store-title {
    display: inline-block;
    font-size: 30rpx;
    font-weight: bold;
    height: 54rpx;
    background: url('http://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/22cb6202506231642544308.png')
      no-repeat;
    background-size: 100% 34rpx;
    background-position: bottom center;
    margin-bottom: 16rpx;
  }

  .store-list {
    // 你可以继续在这里添加嵌套
    .store-card {
      background: #fff;
      border-radius: 16rpx;
      margin-bottom: 24rpx;
      display: flex;
      position: relative;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
      padding: 24rpx;

      &-img {
        position: relative;

        .tag-common {
          position: absolute;
          left: -6rpx;
          top: -6rpx;
          width: 90rpx;
          height: 28rpx;
          background: #c9a063;
          border-radius: 10rpx 0rpx 10rpx 0rpx;
          font-weight: 400;
          font-size: 18rpx;
          color: #f4efe0;
          z-index: 9;

          &::before {
            content: '';
            position: absolute;
            display: block;
            width: 6rpx;
            height: 5rpx;
            left: 0;
            bottom: -5rpx;
            background: linear-gradient(45deg, #ffffff 50%, #050001 50%);
          }
        }
      }

      .store-img {
        width: 128rpx;
        height: 128rpx;
        border-radius: 12rpx;
        margin-right: 10rpx;
      }

      .store-info {
        flex: 1;
        display: flex;
        flex-direction: column;

        .store-header {
          display: flex;
          align-items: center;
          margin-bottom: 8rpx;

          .store-name {
            font-weight: 500;
            font-size: 28rpx;
            color: #333333;
            margin-right: 20rpx;
          }

          .tag-open {
            padding: 4rpx 10rpx;
            background: linear-gradient(0deg, #2f2c24, #755e18);
            border-radius: 8rpx;
            font-weight: 400;
            font-size: 20rpx;
            color: #ffffff;
            margin-right: 10rpx;
          }

          .tag-close {
            background: #f0f0f0;
            color: #999;
            font-size: 22rpx;
            border-radius: 6rpx;
            padding: 2rpx 10rpx;
            margin-right: 8rpx;
          }

          .store-time {
            font-weight: 400;
            font-size: 24rpx;
            color: #999999;
          }
          .store-near {
            font-weight: 400;
            font-size: 20rpx;
            color: #c9a063;
            margin-left: auto;
          }
        }

        .store-address {
          margin-top: 20rpx;
          font-size: 24rpx;
          color: #888;
          &-left {
            font-weight: 400;
            font-size: 20rpx;
            color: #666666;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .iconfont {
              font-size: 26rpx;
              color: #999999;
              margin-left: 6rpx;
            }
          }

          .distance {
            margin-left: 12rpx;
            font-weight: 400;
            font-size: 20rpx;
            color: #333333;
            text-align: right;
            margin-top: 10rpx;
          }
        }
      }
    }
  }
}
</style>
