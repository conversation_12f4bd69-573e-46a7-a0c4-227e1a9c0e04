<template>
  <view class="new-store-page">
    <!-- 自定义顶部背景颜色 -->
    <view class="top">
      <!-- #ifdef MP || APP-PLUS -->
      <view class="sys-head">
        <view class="sys-bar" :style="{ height: sysHeight }"></view>
        <!-- #ifdef MP -->
        <view class="sys-title">门店</view>
        <!-- #endif -->
        <view class="bg"></view>
      </view>
      <!-- #endif -->
    </view>
    <image
      class="map-bg"
      src="http://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/8a35c202507161100575519.png"
      mode="widthFix"
    ></image>
    <view class="new-store-body">
      <!-- 顶部栏：城市选择、搜索框、搜索按钮 -->
      <view class="top-bar">
        <!-- <view class="city-select" @click="onCitySelect">
          <text class="iconfont icon-ic_location1"></text>
          <text class="city-name">
            {{ currentCity }}
          </text>
          <text class="iconfont icon-ic_down2"></text>
        </view> -->
        <view class="search-box">
          <input v-model="searchKeyword" placeholder="搜索门店/项目/发型师" class="search-input" />
          <button class="search-btn center" hover-class="button-hover" @click="onSearch">
            <text class="iconfont icon-ic_search"></text>
          </button>
        </view>
      </view>
      <!-- 门店列表 -->
      <view class="store-list">
        <view
          class="store-card"
          v-for="(store, idx) in merchantList"
          :key="idx"
          @click="handleStore(store)"
        >
          <view class="store-card-img">
            <image :src="store.mer_avatar" class="store-img" mode="aspectFill" />
            <!-- <view v-if="store.common" class="tag-common center">常用</view> -->
          </view>
          <view class="store-info">
            <view class="store-header">
              <text class="store-name">{{ store.mer_name }}</text>
              <text v-if="store.status === '营业中'" class="tag-open">营业中</text>
              <text v-else class="tag-close">休息中</text>
              <text class="store-time">
                {{ store.mer_take_time.join('-') }}
              </text>
            </view>
            <view class="store-score">
              <view class="score center">
                <image src="/static/images/icon-haoping.png" class="icon" mode="widthFix"></image>
                98%好评“服务热情、效果好...”
              </view>
              <view class="comment">
                10条评论
                <text class="iconfont icon-ic_rightarrow"></text>
              </view>
            </view>
            <view class="store-tags">
              <text v-for="(tag, i) in store.tagsList" :key="i" class="tag">
                {{ tag }}
              </text>
            </view>
            <view class="stylist-services">
              <view class="service-item" v-for="service in store.recommend" :key="service.id">
                <view class="price-info">
                  <view class="price">
                    <text>¥</text>
                    {{ service.price }}
                  </view>
                  <view class="discount" v-if="service.discount">{{ service.discount }}折</view>
                  <view class="original-price">¥{{ service.ot_price }}</view>
                </view>
                <text class="service-name">{{ service.store_name }}</text>
              </view>
            </view>
            <view class="store-address">
              <view class="store-address-left">
                <view>
                  {{ store.mer_address }}
                  <text
                    class="iconfont icon-icon_copy"
                    @click="handleCopy(store.mer_address)"
                  ></text>
                </view>
                <view class="distance">距您{{ store.distance }}</view>
              </view>
              <button
                class="reserve-btn center"
                hover-class="button-hover"
                @click.stop="handleAppointment(store)"
              >
                去预约
              </button>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!--自定义底部tab栏-->
    <customTab :newData="newData" :activeRouter="activeRouter"></customTab>
  </view>
</template>

<script>
import customTab from '@/components/customTab'
import { getNavigation } from '@/api/public.js'
import { getMerchantList } from '@/api/hairdressing.js'
let sysHeight = uni.getSystemInfoSync().statusBarHeight + 'px'
export default {
  components: { customTab },
  data() {
    return {
      sysHeight: sysHeight,
      newData: {},
      activeRouter: '',
      currentCity: '上海',
      searchKeyword: '',
      merchantList: [],
      //当前的位置坐标
      location: {
        lng: '',
        lat: '',
      },
    }
  },
  onLoad(options) {
    this.getLocation()
  },
  onShow() {
    let routes = getCurrentPages()
    let curRoute = routes[routes.length - 1].route
    this.activeRouter = '/' + curRoute
    this.getNav()
  },
  onPullDownRefresh() {
    this.getNav()
    this.loadMerchantList()

    // 停止下拉刷新
    setTimeout(() => {
      uni.stopPullDownRefresh()
    }, 1000)
  },
  methods: {
    // 跳转门店详情
    handleStore(store) {
      uni.navigateTo({
        url: `/pages/hairdressing/store/index?store=${JSON.stringify(store)}`,
      })
    },
    // 预约
    handleAppointment(item) {
      uni.navigateTo({
        url: `/pages/hairdressing/appointment/index?info=${JSON.stringify(item)}`,
        // url: `/pages/hairdressing/place_order/index?info=${JSON.stringify(item)}`,
      })
    },
    getNav() {
      getNavigation().then((res) => {
        this.newData = res.data
        if (this.newData.status && this.newData.status.status) {
          uni.hideTabBar()
        } else {
          uni.showTabBar()
        }
      })
    },
    onCitySelect() {
      uni.showToast({ title: '城市选择功能待实现', icon: 'none' })
    },
    onSearch() {
      //调用API获取搜索结果
      this.loadMerchantList()
    },
    getLocation() {
      uni.authorize({
        scope: 'scope.userLocation',
        success: () => {
          uni.getLocation({
            type: 'gcj02',
            success: (res) => {
              this.location.lng = res.longitude
              this.location.lat = res.latitude
            },
            complete: () => {
              this.loadMerchantList()
            },
          })
        },
        fail: (err) => {
          this.loadMerchantList()
        },
      })
    },
    // 获取门店列表
    loadMerchantList() {
      const params = {
        ...this.location,
      }
      if (this.searchKeyword.trim()) {
        params.keyword = this.searchKeyword.trim()
      }
      getMerchantList(params).then((res) => {
        res.data.list.forEach((store) => {
          store.recommend.forEach((item) => {
            item.ot_price = Number(item.ot_price)
            item.price = Number(item.price)
            //根据原价 price和现价 ot_price 算折扣
            if (item.ot_price && item.price && !item.discount) {
              item.discount = ((item.price / item.ot_price) * 10).toFixed(1)
            }
          })
          //处理营业时间
          store.mer_take_timeTxt = store.mer_take_time.join('-')
          store.tagsList = store.tags.split(',')
        })

        this.merchantList = res.data.list
      })
    },
    //处理复制
    handleCopy(text) {
      uni.setClipboardData({
        data: text,
        success() {
          uni.showToast({
            title: '复制成功',
            icon: 'success',
          })
        },
        fail() {
          uni.showToast({
            title: '复制失败',
            icon: 'none',
          })
        },
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.new-store-page {
  background: #f7f7f7;
  min-height: 100vh;
  .map-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 670rpx;
  }
  .new-store-body {
    position: relative;
    z-index: 9;
    padding: 30rpx;
  }
  .sys-title {
    color: #3a3a3a;
  }
  .top-bar {
    display: flex;
    align-items: center;
    height: 68rpx;
    background: #ffffff;
    border-radius: 34rpx;
    margin-bottom: 20rpx;
    padding: 16rpx 0;

    .city-select {
      font-size: 28rpx;
      color: #333;
      margin-right: 24rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      padding-left: 40rpx;

      .iconfont {
        font-size: 24rpx;
      }
      .city-name {
        margin: 0 8rpx;
      }
    }

    .search-box {
      flex: 1;
      display: flex;
      align-items: center;
      border-left: 1px solid #f2f3f7;
      padding-left: 30rpx;
      height: 38rpx;

      .search-input {
        flex: 1;
        height: 56rpx;
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
      }

      .search-btn {
        width: 98rpx;
        height: 58rpx;
        background: #c9a063;
        border-radius: 29rpx;

        .iconfont {
          font-size: 34rpx;
          color: #fff;
        }
      }
    }
  }

  .store-list {
    // 你可以继续在这里添加嵌套
    .store-card {
      background: #fff;
      border-radius: 16rpx;
      margin-bottom: 24rpx;
      display: flex;
      position: relative;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
      padding: 24rpx;

      &-img {
        position: relative;

        .tag-common {
          position: absolute;
          left: -6rpx;
          top: -6rpx;
          width: 48rpx;
          height: 28rpx;
          background: #c9a063;
          border-radius: 10rpx 0rpx 10rpx 0rpx;
          font-weight: 400;
          font-size: 18rpx;
          color: #f4efe0;
          z-index: 9;

          &::before {
            content: '';
            position: absolute;
            display: block;
            width: 6rpx;
            height: 5rpx;
            left: 0;
            bottom: -5rpx;
            background: linear-gradient(45deg, #ffffff 50%, #050001 50%);
          }
        }
      }

      .store-img {
        width: 128rpx;
        height: 128rpx;
        border-radius: 12rpx;
        margin-right: 10rpx;
      }

      .store-info {
        flex: 1;
        display: flex;
        flex-direction: column;

        .store-header {
          display: flex;
          align-items: center;
          margin-bottom: 8rpx;

          .store-name {
            font-weight: 600;
            font-size: 32rpx;
            color: #333333;
            margin-right: 20rpx;
          }

          .tag-open {
            padding: 4rpx 10rpx;
            background: linear-gradient(0deg, #2f2c24, #755e18);
            border-radius: 8rpx;
            font-weight: 400;
            font-size: 20rpx;
            color: #ffffff;
            margin-right: 10rpx;
          }

          .tag-close {
            background: #f0f0f0;
            color: #999;
            font-size: 22rpx;
            border-radius: 6rpx;
            padding: 2rpx 10rpx;
            margin-right: 8rpx;
          }

          .store-time {
            font-weight: 400;
            font-size: 24rpx;
            color: #999999;
          }
        }

        .store-score {
          margin-bottom: 16rpx;
          display: flex;
          align-items: center;

          .score {
            font-size: 20rpx;
            color: #adadad;

            .icon {
              width: 24rpx;
              height: 24rpx;
              margin-right: 10rpx;
            }
          }

          .comment {
            color: #967c68;
            margin-left: 8rpx;
            font-weight: 400;
            font-size: 20rpx;
            display: flex;
            align-items: center;

            .iconfont {
              margin-left: 8rpx;
              font-size: 22rpx;
              color: #999999;
              font-weight: bold;
            }
          }
        }

        .store-tags {
          margin-bottom: 28rpx;
          display: flex;
          align-items: center;
          column-gap: 20rpx;

          .tag {
            background: #f4efe0;
            border-radius: 6rpx;
            font-weight: 400;
            font-size: 20rpx;
            color: #927d6b;
            padding: 6rpx;
          }
        }

        .stylist-services {
          background: #fafafa;
          border-radius: 12rpx;
          padding: 6rpx;
          width: 100%;
          .service-item {
            display: flex;
            align-items: center;
            margin-bottom: 10rpx;

            .price-info {
              display: flex;
              align-items: center;
              flex-shrink: 0; // 防止价格部分被压缩
              .price {
                font-size: 32rpx;
                font-weight: bold;
                color: #a01c1c;

                text {
                  font-size: 22rpx;
                }
              }

              .discount {
                font-size: 20rpx;
                color: #a01c1c;
                padding: 2rpx 6rpx;
                margin: 0 10rpx;
                background: #f7e9e9;
                border-radius: 3rpx;
                position: relative;

                &::before {
                  content: '';
                  position: absolute;
                  left: -6rpx;
                  top: 50%;
                  transform: translateY(-50%);
                  width: 0;
                  height: 0;
                  border-style: solid;
                  border-width: 8rpx 8rpx 8rpx 0;
                  border-color: transparent #f7e9e9 transparent transparent;
                }
              }

              .original-price {
                font-size: 22rpx;
                color: #cccccc;
                text-decoration: line-through;
              }
            }

            .service-name {
              font-size: 26rpx;
              color: #333;
              margin-left: 20rpx;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              flex: 1; // 让服务名称占据剩余空间
              width: 0;
            }
          }
        }

        .store-address {
          margin-top: 20rpx;
          font-size: 24rpx;
          color: #888;
          display: flex;
          align-items: center;
          justify-content: space-between;

          &-left {
            font-weight: 400;
            font-size: 20rpx;
            color: #666666;

            .iconfont {
              font-size: 26rpx;
              color: #999999;
              margin-left: 6rpx;
            }
          }

          .distance {
            margin-left: 12rpx;
            font-weight: 400;
            font-size: 20rpx;
            color: #c9a063;
            text-align: right;
            margin-top: 10rpx;
          }

          .reserve-btn {
            width: 128rpx;
            height: 48rpx;
            background: #c9a063;
            border-radius: 24rpx;
            font-weight: 500;
            font-size: 24rpx;
            color: #ffffff;
          }
        }
      }
    }
  }
}
</style>
