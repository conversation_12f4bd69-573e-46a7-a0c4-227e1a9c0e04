<template>
  <view class="exchange-store">
    <!-- 御享值 -->
    <view class="exchange-store__top">
      <view class="exchange-store__coin">
        <image
          class="exchange-store__coin-img"
          src="/static/images/icon-jifen.png"
          mode="aspectFill"
        />
        <view class="">
          <view class="exchange-store__coin-label">
            我的御享值
            <view class="rules center" @click="openRulesPopup">
              规则
              <image src="/static/images/icon-rule.png" class="rules-img" mode="widthFix"></image>
            </view>
          </view>
          <view class="exchange-store__score">21</view>
        </view>
      </view>
    </view>
    <!-- 明细列表 -->
    <view class="details">
      <view class="details__title">御享值明细</view>
      <view class="details__list">
        <view class="details__list__item">
          <view class="purpose">
            <view class="purpose-name">商城兑换</view>
            <view class="purpose-time">2025-05-06 12:00:00</view>
          </view>
          <view class="price">-599</view>
        </view>
        <emptyPage
          title="暂无御享值记录哦～"
          :noImage="`${domain}/static/images/noRecord.png`"
        ></emptyPage>
      </view>
    </view>
    <!-- 积分规则弹窗 -->
    <uni-popup ref="rulesPopup" type="bottom">
      <view class="rulesPopup">
        <view class="rulesPopup__banner">
          <image
            class="img"
            src="http://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/147a6202506251647109502.png"
            mode="aspectFill"
          ></image>
          <text class="iconfont icon-ic_close1" @click="closeRulesPopup"></text>
        </view>
        <view class="bg-white">
          <view class="rulesPopup__title">御享值规则</view>
          <view class="rulesPopup__text">
            <jyf-parser :html="protocol" ref="article" :tag-style="tagStyle"></jyf-parser>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { getAgreementApi } from '@/api/user.js'
import parser from '@/components/jyf-parser/jyf-parser'
import emptyPage from '@/components/emptyPage.vue'
import { HTTP_REQUEST_URL } from '@/config/app'
export default {
  components: { 'jyf-parser': parser, emptyPage },
  data() {
    return {
      domain: HTTP_REQUEST_URL,
      protocol: '',
      tagStyle: {
        img: 'width:100%;display:block;',
        video: 'width:100%;'
      }
    }
  },
  onLoad() {
    this.getAgreement()
  },
  methods: {
    openRulesPopup() {
      this.$refs.rulesPopup.open()
    },
    closeRulesPopup() {
      this.$refs.rulesPopup.close()
    },
    getAgreement() {
      getAgreementApi('sys_integral_rule').then(res => {
        console.log('res', res)
        this.protocol = res.data.sys_integral_rule
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.exchange-store {
  min-height: 100vh;
  background: linear-gradient(0deg, #f2f3f7 0%, #ffefd6 100%);
  padding: 28rpx;
  &__top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
  }
  &__coin {
    display: flex;
    align-items: center;
  }
  &__coin-img {
    width: 68rpx;
    height: 68rpx;
    margin-right: 18rpx;
    vertical-align: middle;
  }
  &__coin-label {
    font-size: 26rpx;
    color: #333;
    margin-right: 4rpx;
    display: flex;
    align-items: center;
    column-gap: 8rpx;
    .rules {
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      margin-left: 10rpx;
    }
    .rules-img {
      width: 24rpx;
      height: 24rpx;
      margin-left: 10rpx;
    }
  }

  &__score {
    font-weight: 600;
    font-size: 36rpx;
    color: #333333;
  }
  .details {
    margin-top: 40rpx;
    padding: 40rpx 20rpx;
    background: #ffffff;
    border-radius: 30rpx;
    &__title {
      display: inline-block;
      font-size: 30rpx;
      font-weight: bold;
      background: url('http://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/22cb6202506231642544308.png')
        no-repeat;
      background-size: 100% 34rpx;
      height: 54rpx;
      background-position: bottom center;
    }
    &__list {
      &__item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1rpx solid #f2f3f7;
        padding: 26rpx 0;
        .purpose {
          &-name {
            font-weight: 500;
            font-size: 24rpx;
            color: #333333;
            margin-bottom: 8rpx;
          }
          &-time {
            font-weight: 300;
            font-size: 22rpx;
            color: #999999;
          }
        }
        .price {
          font-weight: 600;
          font-size: 30rpx;
          color: #333333;
        }
      }
    }
  }
}
.bg-white {
  background-color: white;
}
.rulesPopup {
  &__banner {
    position: relative;
    .iconfont {
      position: absolute;
      right: 96rpx;
      top: 48rpx;
      color: #999999;
      font-size: 48rpx;
    }
    .img {
      width: 100%;
      height: 180rpx;
      vertical-align: bottom;
    }
  }
  &__title {
    padding-top: 10rpx;
    margin-top: -6rpx;
    text-align: center;
    font-weight: 600;
    font-size: 30rpx;
    color: #333333;
  }
  &__text {
    padding: 60rpx 30rpx;
    min-height: 600rpx;
  }
}
</style>
