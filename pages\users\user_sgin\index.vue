<template>
  <view :style="viewColor" class="relative">
    <!-- 自定义顶部背景颜色 -->
    <view class="top">
      <!-- #ifdef MP || APP-PLUS -->
      <view class="sys-head fixed">
        <view class="sys-bar" :style="{ height: sysHeight }"></view>
        <!-- #ifdef MP -->
        <view class="sys-title">
          <view class="iconfont icon-ic_leftarrow" @tap="goBack"></view>
          签到
        </view>
        <!-- #endif -->
        <view class="bg"></view>
      </view>
      <!-- #endif -->
    </view>
    <image
      class="icon-img"
      src="http://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/7ad6e202506260944236302.png"
      mode="widthFix"
    ></image>
    <view class="banner"></view>
    <view class="sign" :style="{ marginTop: marTop }">
      <view class="header">
        <view class="headerCon acea-row row-between-wrapper">
          <view class="left acea-row row-between-wrapper">
            <view class="pictrue">
              <image src="/static/images/icon-jifen.png"></image>
            </view>
            <view class="text">
              <view class="line1">
                我的御享值
                <text class="iconfont icon-ic_rightarrow"></text>
              </view>
              <view class="integral acea-row">
                <text>{{ userInfo.integral ? userInfo.integral : 0 }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- 签到天数 -->
      <view class="sign-card">
        <view class="check-day">
          <view class="day center">
            已连续签到
            <text class="day-num">
              <view class="item" v-if="signCount[0]">{{ signCount[0] }}</view>
              <view class="item" v-if="signCount[1]">{{ signCount[1] }}</view>
              <view class="item" v-if="signCount[2]">{{ signCount[2] }}</view>
              <view class="item">{{ signCount[3] || 0 }}</view>
            </text>
            天
            <view class="chamfer"></view>
          </view>
          <view class="check-btn">
            <button hover-class="btnHoverClass" class="btn" @click="toExchangeMall">
              兑换商城
            </button>
          </view>
        </view>
        <view class="wrapper">
          <view class="list acea-row row-between-wrapper">
            <view class="item" v-for="(item, index) in signConfig" :key="index">
              <view class="num" :class="sign_index >= index + 1 ? 'venusSelect' : ''">
                <view class="num-integral">+{{ item.value.sign_integral }}</view>
                <view class="num-name">御享值</view>
                <view class="sign-progress-dot"></view>
                <view class="sign-day">{{ item.value.day }}天</view>
              </view>
            </view>
          </view>
          <button class="but b-color on" v-if="is_sign">已签到</button>
          <form @submit="goSign" report-submit="true" v-else>
            <button class="but b-color" :class="disabled ? 'disabled' : ''" formType="submit">
              立即签到
            </button>
          </form>
        </view>
      </view>
      <!-- 任务列表 -->
      <view class="privilege-tasks">
        <view class="privilege-tasks__title">获取更多御享值</view>
        <view class="privilege-tasks__list">
          <view class="privilege-tasks__item" v-for="(item, idx) in tasks" :key="idx">
            <view class="privilege-tasks__item-icon center">
              <image
                class="privilege-tasks__item-icon-img"
                :src="item.icon"
                mode="widthFix"
              ></image>
            </view>
            <view class="privilege-tasks__item-info">
              <view class="privilege-tasks__item-title">
                {{ item.title }}
                <text class="subtitle" v-if="item.subtitle">{{ item.subtitle }}</text>
              </view>
              <view class="privilege-tasks__item-desc">{{ item.desc }}</view>
            </view>
            <button class="privilege-tasks__item-btn">{{ item.btn }}</button>
          </view>
        </view>
      </view>
      <!-- 提示签成功 -->
      <uni-popup ref="signTipPopup" type="center">
        <view class="signTipPopup">
          <view class="z-90">
            <view class="signTipPopup__icon">
              <image
                class="img"
                src="http://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/41dad202506261548131602.png"
                mode="widthFix"
              ></image>
            </view>
            <view class="signTipPopup__title">签到成功</view>
            <view class="signTipPopup__integral">+{{ integral }}御享值</view>
            <button class="signTipPopup__bnt center" hover-class="btnHoverClass" @click="close">
              开心收下
            </button>
          </view>
        </view>
      </uni-popup>
    </view>
  </view>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
let sysHeight = uni.getSystemInfoSync().statusBarHeight + 'px'
import { mapGetters } from 'vuex'
import { getSignUser, getSignList, setSignIntegral } from '@/api/user.js'
import { HTTP_REQUEST_URL } from '@/config/app'
import { toLogin } from '@/libs/login.js'
export default {
  components: {},
  data() {
    return {
      sysHeight: sysHeight,
      //#ifdef H5
      marTop: 0,
      //#endif
      //#ifndef H5
      marTop: uni.getSystemInfoSync().statusBarHeight + 43 + 'px',
      //#endif
      domain: HTTP_REQUEST_URL,
      userInfo: {},
      signConfig: {},
      signCount: [],
      signSystemList: [],
      signList: [],
      integral: 0,
      day: 0,
      sign_index: 0,
      is_sign: false, //是否签到
      disabled: false,
      tasks: [
        {
          icon: '/static/images/icon-signin.png',
          title: '每日签到',
          desc: '每月最高可获得 200 御享值',
          btn: '去签到'
        },
        {
          icon: '/static/images/icon-pay.png',
          title: '去消费',
          desc: '消费1元=1御享值',
          btn: '去美发'
        },
        {
          icon: '/static/images/icon-recharge.png',
          title: '余额储值',
          desc: '最高可获得1000御享值',
          btn: '去充值'
        },
        {
          icon: '/static/images/icon-invite.png',
          title: '邀请好友',
          subtitle: '好友首次下单',
          desc: '好友首次下单 +300 御享值',
          btn: '去邀请'
        },
        {
          icon: '/static/images/icon-rate.png',
          title: '消费评价',
          desc: '最高可获得+300御享值',
          btn: '去评价'
        },
        {
          icon: '/static/images/icon-feedback.png',
          title: '意见反馈',
          desc: '+300御享值',
          btn: '去反馈'
        },
        {
          icon: '/static/images/icon-info.png',
          title: '完善资料',
          desc: '+200御享值',
          btn: '去完善'
        }
      ]
    }
  },
  computed: mapGetters(['isLogin', 'viewColor']),
  onLoad() {
    if (this.isLogin) {
      this.getUserInfo()
      this.getSignList()
    } else {
      toLogin()
    }
  },
  methods: {
    // 兑换商城
    toExchangeMall() {
      uni.navigateTo({ url: '/pages/hairdressing/exchange_mall/index' })
      // return this.$util.Tips('/pages/users/user_exchange/index')
    },
    goBack: function () {
      uni.navigateBack()
    },
    /**
     * 去签到记录页面
     *
     */
    goSignList: function () {
      return this.$util.Tips('/pages/users/user_sgin_list/index')
    },
    /**
     * 获取用户信息
     */
    getUserInfo: function () {
      let that = this
      getSignUser().then(res => {
        res.data.integral = parseInt(res.data.integral)
        let sum_sgin_day = res.data.count
        res.data.title.map((item, index) => {
          item.value.day = index + 1
        })
        console.log('signConfig', res.data.title)
        that.$set(that, 'signConfig', res.data.title)
        that.$set(that, 'is_sign', res.data.is_sign)
        that.$set(that, 'userInfo', res.data.userInfo)
        that.signCount = that.PrefixInteger(sum_sgin_day, 4)
        that.sign_index = res.data.sign_num
      })
    },
    /**
     * 获取签到列表
     *
     */
    getSignList: function () {
      let that = this
      getSignList({ page: 1, limit: 3 }).then(res => {
        that.$set(that, 'signList', res.data.list)
      })
    },
    /**
     * 数字转中文
     *
     */
    Rp: function (n) {
      let cnum = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
      let s = ''
      n = '' + n // 数字转为字符串
      for (let i = 0; i < n.length; i++) {
        s += cnum[parseInt(n.charAt(i))]
      }
      return s
    },

    /**
     * 数字分割为数组
     * @param int num 需要分割的数字
     * @param int length 需要分割为n位数组
     */
    PrefixInteger: function (num, length) {
      return (Array(length).join('0') + num).slice(-length).split('')
    },
    /**
     * 用户签到
     */
    goSign(e) {
      let that = this,
        sum_sgin_day = that.signConfig.sign_num
      if (that.userInfo.is_day_sgin) return this.$util.Tips({ title: '您今日已签到!' })
      that.disabled = true
      setSignIntegral()
        .then(res => {
          this.$refs.signTipPopup.open()
          that.integral = res.data.integral
          that.sign_index =
            that.sign_index + 1 > that.signConfig.title.length ? 1 : that.sign_index + 1
          that.signCount = that.PrefixInteger(sum_sgin_day + 1, 4)
          that.$set(that.userInfo, 'is_sgin', true)
          that.$set(
            that.userInfo,
            'integral',
            that.$util.$h.Add(that.userInfo.integral, res.data.integral)
          )
        })
        .catch(err => {
          that.disabled = false
          return this.$util.Tips({ title: err })
        })
    },
    /**
     * 关闭签到提示
     */
    close() {
      this.$refs.signTipPopup.close()
      this.getSignList()
      this.getUserInfo()
    }
  }
}
</script>

<style scoped lang="scss">
.relative {
  position: relative;
  background: #fafafa;
  min-height: 100vh;
}
.sys-title,
.icon-ic_leftarrow {
  color: #333;
}
.icon-img {
  position: absolute;
  top: 46prx;
  right: 71rpx;
  width: 198rpx;
  height: 310rpx;
  z-index: 2;
}
.sys-head .bg {
  width: 100%;
  background: #ffeed3;
}

.sign .header {
  width: 100%;
  padding-bottom: 10rpx;
}
.sign .header .headerCon {
  padding: 94rpx 0 40rpx 30rpx;
}
.sign .header .headerCon .left {
  font-size: 26rpx;
  color: #333;
  margin-right: 4rpx;
  display: flex;
  align-items: center;
  column-gap: 8rpx;
  .iconfont {
    color: #666;
    font-size: 22rpx;
    padding-top: 4rpx;
  }
}
.sign .header .headerCon .left .integral text {
  font-weight: 600;
  font-size: 36rpx;
  color: #333333;
}
.sign .header .headerCon .text {
  width: 410rpx;
}
.sign .header .headerCon .left .pictrue {
  width: 68rpx;
  height: 68rpx;
  border-radius: 50%;
  margin-right: 18rpx;
}
.sign .header .headerCon .left .pictrue image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.sign .header .headerCon .right {
  width: 142rpx;
  height: 66rpx;
  background-color: #fff;
  border-radius: 50rpx 0 0 50rpx;
  font-size: 24rpx;
  color: #ff9000;
}
.sign .header .headerCon .right .iconfont {
  font-size: 28rpx;
  margin-right: 8rpx;
}
.check-day {
  display: flex;
  align-items: flex-start;
  .day {
    width: 380rpx;
    height: 92rpx;
    background: #ffffff;
    border-radius: 30rpx 30rpx 0rpx 0rpx;
    font-weight: 600;
    font-size: 36rpx;
    color: #999999;
    position: relative;
    &-num {
      color: #333;
      margin: 0 6rpx;
      font-size: 46rpx;
    }
    .chamfer {
      width: 25rpx;
      height: 24rpx;
      background: #ffffff;
      position: absolute;
      right: -24rpx;
      bottom: 0;
      overflow: hidden;
      &::after {
        content: ' ';
        display: block;
        width: 50rpx;
        height: 50rpx;
        background: #fcf4e8;
        position: absolute;
        border-radius: 50%;
        left: 0;
        bottom: 0;
      }
    }
  }
  .check-btn {
    margin-left: 34rpx;
    .btn {
      background: #c9a063;
      border-radius: 30rpx 30rpx 30rpx 18rpx;
      font-weight: 400;
      font-size: 26rpx;
      color: #ffffff;
      padding: 20rpx 64rpx;
    }
  }
}
.sign {
  position: relative;
  z-index: 6;
}
.sign .wrapper {
  background-color: #fff;
  padding: 10rpx 0 80rpx;
  position: relative;
  border-radius: 0rpx 30rpx 30rpx 30rpx;
}
.sign .wrapper .list {
  // padding: 0 30rpx;
  margin-bottom: 80rpx;
}
.sign .wrapper .list .item {
  font-size: 22rpx;
  color: #999999;
  text-align: center;
  position: relative;
  &:first-child {
    margin-left: 30rpx;
    .num {
      &::after {
        width: 34rpx;
        left: 4rpx;
        border-radius: 2rpx;
      }
    }
  }
  &:last-child {
    margin-right: 30rpx;
    &::before {
      content: '';
      display: block;
      width: 30rpx;
      height: 4rpx;
      position: absolute;
      left: 42rpx;
      bottom: -22rpx;
      background-color: #e6e6e6;
      border-radius: 2rpx;
    }
  }
}

.sign .wrapper .list .item .num {
  width: 80rpx;
  height: 80rpx;
  background: #fafafa;
  border-radius: 12rpx;
  position: relative;
  color: #cccccc;
  border: 2px solid transparent;

  .num-integral {
    font-weight: 600;
    font-size: 30rpx;
  }
  .num-name {
    font-weight: 300;
    font-size: 18rpx;
  }
  &::after {
    content: ' ';
    position: absolute;
    display: block;
    width: 92rpx;
    height: 4rpx;
    bottom: -26rpx;
    left: -58rpx;
    background-color: #e6e6e6;
  }
}
.venusSelect {
  border: 2px solid #c9a063;
  .num-integral {
    color: #c9a063;
  }
  .num-name {
    color: #333;
  }
  .sign-progress-dot {
    background-color: #c9a063 !important;
  }
  &::after {
    background-color: #c9a063 !important;
  }
}
.signday {
  font-weight: 300;
  font-size: 26rpx;
  color: #666666;
}
.venusSelect + .signday {
  color: #c9a063;
}
.sign .wrapper .but {
  width: 650rpx;
  height: 78rpx;
  font-size: 30rpx;
  line-height: 78rpx;
  color: #fff;
  border-radius: 40rpx;
  text-align: center;
  margin: 0 auto;
  background-color: #c9a063;
}
.sign .wrapper .but.disabled {
  pointer-events: none;
}
.sign .wrapper .but.on {
  background-color: #999 !important;
}

.sign-card {
  padding: 0 28rpx;
}
.banner {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 526rpx;
  background: linear-gradient(0deg, #fafafa 0%, #ffefd6 84%, #ffefd6 100%);
}
.sign-day {
  position: absolute;
  bottom: -66rpx;
  left: 50%;
  transform: translateX(-50%);
  font-weight: 300;
  font-size: 18rpx;
  color: #666666;
}
.sign-progress-dot {
  position: absolute;
  bottom: -28.5rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 10rpx;
  height: 10rpx;
  background: #eee;
  border-radius: 50%;
  z-index: 20;
}
.sign-progress-dot.active {
  background: #c9a063;
  border-color: #c9a063;
}
.privilege-tasks {
  background: linear-gradient(180deg, #fffaf1, #fafafa);
  border-radius: 30rpx;
  padding: 40rpx 20rpx;
  margin: 20rpx 30rpx;

  &__title {
    font-size: 30rpx;
    color: #c9a063;
    font-weight: 600;
    margin-bottom: 40rpx;
  }

  &__list {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
  }

  &__item {
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: 18rpx;
    padding: 20rpx;
    gap: 10rpx;
  }

  &__item-icon {
    width: 68rpx;
    height: 68rpx;
    background: #fffbf4;
    border-radius: 12rpx;
    flex-shrink: 0;
    margin-right: 10rpx;
    &-img {
      width: 46rpx;
      height: 46rpx;
    }
  }

  &__item-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4rpx;
  }
  &__item-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #222;
    margin-bottom: 2rpx;
    .subtitle {
      font-weight: 300;
      font-size: 22rpx;
      color: #999999;
      margin-left: 10rpx;
    }
  }
  &__item-desc {
    font-size: 22rpx;
    color: #c9a063;
    margin-top: 0rpx;
  }

  &__item-btn {
    background: #c9a063;
    color: #fff;
    font-size: 24rpx;
    border-radius: 24rpx;
    padding: 0 32rpx;
    height: 48rpx;
    line-height: 48rpx;
    font-weight: 600;
    flex-shrink: 0;
  }
}
.signTipPopup {
  width: 529rpx;
  height: 529rpx;
  background: linear-gradient(0deg, #ffffff 0%, #ffefd5 51%, #ffefd5 99%);
  border-radius: 68rpx;
  position: relative;
  &::after {
    content: ' ';
    display: block;
    position: absolute;
    width: 530rpx;
    height: 530rpx;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 68rpx;
    border: 1px solid #ffffff;
    left: 0;
    top: 0;
    transform: rotate3d(1, 1, 1, 15deg);
  }
  &__icon {
    width: 100%;
    position: absolute;
    top: -80rpx;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    .img {
      width: 232rpx;
      height: 212rpx;
    }
  }
  &__title {
    padding-top: 140rpx;
    font-weight: 600;
    font-size: 68rpx;
    color: #c9a063;
    text-align: center;
    margin-top: 34rpx;
  }
  &__integral {
    font-weight: 600;
    font-size: 38rpx;
    color: #a41111;
    margin-top: 40rpx;
    text-align: center;
  }
  &__bnt {
    width: 368rpx;
    height: 88rpx;
    background: #c9a063;
    border-radius: 44rpx;
    font-weight: 400;
    font-size: 30rpx;
    color: #ffffff;
    margin: 60rpx auto 0;
  }
}
.z-90 {
  position: relative;
  z-index: 90;
}
</style>
