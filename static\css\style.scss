.font-bg-red,
.font_bg-red {
  display: inline-block;
  text-align: center;
  padding: 0 4rpx;
  line-height: 28rpx;
  background: #e93424;
  color: #fff;
  font-size: 20rpx;
  border-radius: 5rpx;
  margin-right: 8rpx;
}
.type2 {
  background-color: #fd6523;
}
.bg {
  font-size: 24rpx;
  line-height: 31rpx;
}
.ml8 {
  margin-left: 8rpx;
}
.boder-24 {
  border-radius: 24rpx;
}
.boder-44 {
  border-radius: 40rpx 40rpx 0 0;
}
.top30 {
  position: relative;
  top: -30rpx;
}
.pos-rel {
  position: relative;
}
.bg-f {
  background: #fff;
}
.font-bold {
  font-weight: bold;
}
.font-500 {
  font-weight: 500;
}
.popup-main {
  position: fixed;
  bottom: 0;
  width: 100%;
  left: 0;
  background-color: #fff;
  z-index: 280;
  border-radius: 40rpx 40rpx 0 0;
  transform: translate3d(0, 100%, 0);
  transform: translateY(100%);
  transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
  &.on {
    transform: translate3d(0, 0, 0);
    transform: translateY(0);
  }
  > .title {
    font-size: 32rpx;
    text-align: center;
    position: relative;
  }
}
.content-height {
  height: 120rpx;
  height: calc(120rpx+ constant(safe-area-inset-bottom)); ///兼容 IOS<11.2/
  height: calc(120rpx + env(safe-area-inset-bottom)); ///兼容 IOS>11.2/
}
.detail-count {
  border-radius: 44rpx 44rpx 0 0;
  background: linear-gradient(180deg, #ffffff 0%, #ffffff 54%, rgba(255, 255, 255, 0) 100%);
}
.swiper-bg {
  border-radius: 44rpx 44rpx 0 0;
  background: #f5f5f5;
  top: -2px;
}
.product-con .conter img {
  width: 100% !important;
  height: unset !important;
}
.product-con .wrapper {
  margin-bottom: 0;
  width: 100%;
  box-sizing: border-box;
}
.product-con .nav {
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 100%;
  height: 100rpx;
  padding: 0 30rpx;
  box-sizing: border-box;
  top: -2px;
}
.product-con .nav .money {
  font-size: 28rpx;
  color: #fff;
}
.product-con .nav .money .num {
  font-size: 48rpx;
}
.product-con .nav .money .y-money {
  font-size: 26rpx;
  margin-left: 10rpx;
  text-decoration: line-through;
}
.product-con .wrapper .share {
  padding-top: 25rpx;
}
.product-con .wrapper .share .money {
  font-size: 28rpx;
}
.product-con .wrapper .share .money .num {
  font-size: 48rpx;
}
.product-con .wrapper .share .money image {
  width: 65rpx;
  height: 28rpx;
  margin-left: 7rpx;
  position: relative;
  top: 5rpx;
}
.product-con .wrapper .share .money .vip-money {
  color: #282828;
  margin-left: 13rpx;
}
.atmosphere .vip-money {
  color: #fff;
  margin-left: 13rpx;
}
.product-con .wrapper .share .money .vip-image,
.atmosphere .vip-image {
  width: 70rpx;
  height: 30rpx;
  margin-left: 6rpx;
  position: relative;
  top: 4rpx;
}
.product-con .wrapper .share .iconfont {
  color: #282828;
  font-size: 30rpx;
  display: inline-block;
  margin-bottom: 10rpx;
}
.product-con .wrapper .introduce {
  font-size: 30rpx;
  font-weight: 500;
}
.product-con .wrapper .introduce {
  align-items: center;
  position: relative;
  margin-bottom: 30rpx;
}
.product-con .wrapper .fenxiang_btn {
  text-align: center;
  font-size: 18rpx;
  color: #999999;
  font-weight: normal;
  display: flex;
  &.btn_one {
    display: block;
  }
  > view {
    &:first-child {
      margin-right: 43rpx;
    }
  }
  .iconfont {
    font-size: 34rpx;
    color: #282828;
  }
  .icon-ic_star1 {
    color: var(--view-theme);
  }
}
.product-con .wrapper .introduce .infor {
  width: 570rpx;
}
.product-con .wrapper .introduce .iconfont {
  font-size: 30rpx;
  display: inline-block;
  margin-bottom: 10rpx;
}
.product-con .wrapper .label {
  font-size: 22rpx;
  color: #999999;
  margin-top: 22rpx;
}
.product-con .wrapper .label .rank_list {
  width: 100%;
  background: #ffffff;
  border-radius: 10rpx;
  height: 56rpx;
  line-height: 56rpx;
  padding: 0 26rpx 0 14rpx;
  justify-content: space-between;
  font-size: 22rpx;
  color: #282828;
}
.product-con .wrapper .rank_list .rank_title {
  width: 108rpx;
  height: 36rpx;
  background-image: url('data:image/png;base64,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');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin-right: 10rpx;
}
.product-con .wrapper .rank_list .iconfont {
  width: 24rpx;
  height: 24rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12rpx;
  color: #fff;
  background: #666666;
  margin-left: 10rpx;
}
.product-con .coupon {
  padding: 0 20rpx;
  height: 88rpx;
  font-size: 26rpx;
  color: #999999;
}
.product-con .coupon .hide {
  width: 640rpx;
  height: 80rpx;
  line-height: 80rpx;
}
.product-con .coupon .activity {
  height: 40rpx;
  padding: 0 20rpx;
  border: 1px solid var(--view-theme);
  color: var(--view-theme);
  font-size: 24rpx;
  display: flex;
  align-items: center;
  position: relative;
  margin: 19rpx 0 19rpx 15rpx;
}
.product-con .coupon .activity:before {
  content: ' ';
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  border-radius: 100%;
  background-color: #fff;
  bottom: 50%;
  left: -4rpx;
  margin-bottom: -7rpx;
  border: 1rpx solid var(--view-theme);
  border-left-color: #fff;
}
.product-con .coupon .activity:after {
  content: ' ';
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  border-radius: 100%;
  background-color: #fff;
  right: -6rpx;
  bottom: 50%;
  margin-bottom: -7rpx;
  border: 1rpx solid var(--view-theme);
  border-right-color: #fff;
}
.product-con .coupon .iconfont {
  color: #999999;
  font-size: 28rpx;
}
.product-con .attribute {
  padding: 0 20rpx;
  color: #999999;
  height: 88rpx;
  position: relative;
}
.product-con .attribute .atterTxt {
  font-size: 26rpx;
  color: #282828;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 520rpx;
  line-height: 88rpx;
}
.product-con .attribute .params {
  margin-right: 20rpx;
  font-size: 26rpx;
  color: #282828;
  &:nth-child(2) {
    margin-right: 0;
  }
}
.product-con .attribute .iconfont {
  font-size: 28rpx;
  line-height: 88rpx;
}
.product-con .guaranteeAttr {
  display: inline-block;
  width: 530rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.product-con .userEvaluation {
  margin-top: 20rpx;
  background-color: #fff;
}
.product-con .userEvaluation .title,
.product-con .combo .title {
  height: 86rpx;
  font-size: 28rpx;
  color: #282828;
  margin-left: 20rpx;
  padding-right: 20rpx;
}
.product-con .userEvaluation .evaluateWtapper {
  margin-top: 0;
}
.product-con .userPlant .title {
  height: 86rpx;
  font-size: 28rpx;
  color: #282828;
  margin-left: 30rpx;
  padding-right: 30rpx;
}
.product-con .userEvaluation .title .praise,
.product-con .userPlant .title .praise,
.product-con .combo .title .praise {
  font-size: 28rpx;
  color: #999;
}
.product-con .userEvaluation .title .praise .iconfont,
.product-con .userPlant .title .praise .iconfont,
.product-con .combo .title .praise .iconfont {
  color: #7a7a7a;
  font-size: 28rpx;
  vertical-align: 1rpx;
  margin-left: 8rpx;
}
.product-con .userPlant {
  background-color: #fff;
}
.product-con .userPlant .imgList {
  padding: 0 30rpx;
}
.product-con .combo .img-box {
  margin: 10rpx 0 30rpx 0;
  display: flex;
}
.product-con .combo .img-box .img-item,
.product-con .combo .combo_item {
  display: flex;
  align-items: center;
}
.product-con .combo .combo_item {
  padding-left: 30rpx;
  position: relative;
}
.product-con .combo .combo_item::after {
  content: '';
  width: 1rpx;
  height: 106rpx;
  background: #e8e8e8;
  position: absolute;
  top: 5rpx;
  right: 0;
}
.product-con .combo .combo_item:last-child::after {
  display: none;
}
.product-con .combo .icon-ic_increase {
  color: #666666;
  margin: 0 18rpx;
  font-size: 16rpx;
}
.product-con .combo .list_total {
  padding: 0 30rpx;
}
.product-con .combo .list_total .list_num {
  font-size: 22rpx;
}
.product-con .combo .list_total .list_price {
  font-size: 26rpx;
  margin-top: 10rpx;
  font-weight: bold;
}
.product-con .combo .img-box .img-item uni-image,
.product-con .combo .img-box .img-item image {
  width: 116rpx;
  height: 116rpx;
  border-radius: 16rpx;
}
.product-con .userPlant .imgList .pictrue {
  width: 202rpx;
  height: 202rpx;
  margin: 0 22rpx 15rpx 0;
  position: relative;
  border-radius: 16rpx;
  position: relative;
}
.product-con .userPlant .imgList .pictrue:last-child {
  margin-right: 0;
}
.product-con .userPlant .imgList .pictrue .image {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
}
.product-con .userPlant .imgList .video_img {
  width: 40rpx;
  height: 40rpx;
  border-radius: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  z-index: 10;
}
.product-con .userPlant .imgList .video_img .iconfont {
  font-size: 20rpx;
  color: #fff;
}
.product-con .product-intro {
  position: relative;
  margin-top: 20rpx;
  width: 100%;
  overflow: hidden;
  background-color: #fff;
  border-radius: 24rpx;
}
.product-con .product-intro .title {
  font-size: 30rpx;
  color: #282828;
  height: 86rpx;
  width: 100%;
  text-align: center;
  line-height: 86rpx;
}
.product-con .product-intro .conter {
  width: 100%;
  overflow: hidden;
}
.product-con .product-intro .conter image {
  width: 100% !important;
  display: block !important;
}
.product-con .price-info {
  background: #fff;
  padding: 30rpx 20rpx 20rpx;
}
.product-con .price-info .price-title {
  color: #666;
  font-size: 28rpx;
  margin-bottom: 20rpx;
}
.product-con .store-wrapper {
  margin-top: 20rpx;
  background-color: #fff;
  .store-hd {
    padding: 30rpx 20rpx;
    .store-info {
      position: relative;
      display: flex;
      .logo {
        width: 86rpx;
        height: 86rpx;
        image {
          width: 86rpx;
          height: 86rpx;
          border-radius: 6rpx;
        }
      }
      .info {
        display: flex;
        flex-direction: column;
        justify-content: center;
        margin-left: 20rpx;
        .name {
          font-size: 28rpx;
          color: #282828;
          font-weight: bold;
        }
        .txt {
          margin-top: 8rpx;
          color: #666666;
          font-size: 22rpx;
        }
      }
      .link {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        align-items: center;
        justify-content: center;
        width: 112rpx;
        height: 56rpx;
        background-image: linear-gradient(
          -90deg,
          var(--view-bntColor21) 0%,
          var(--view-bntColor22) 100%
        );
        border-radius: 100rpx;
        color: #fff;
        font-size: 24rpx;
      }
    }
  }
}
.store-wrapper {
  .con-box {
    padding: 20rpx 20rpx 0;
    .title {
      font-size: 28rpx;
      color: #282828;
    }
    .moer-btn {
      font-size: 28rpx;
      color: #999999;
    }
    .img-box {
      .img-item {
        width: 212rpx;
        margin: 30rpx 15rpx 0 0;
        display: inline-block;
        &:nth-child(3n) {
          margin-right: 0;
        }
        /deep/image,
        /deep/uni-image,
        /deep/.easy-loadimage {
          width: 212rpx;
          height: 212rpx;
          border-radius: 16rpx;
          display: inline-block;
        }
        .txt {
          .title {
            margin-top: 20rpx;
          }
          .price {
            color: var(--view-priceColor);
            margin-top: 6rpx;
          }
        }
      }
    }
    /deep/.uni-swiper-dot,
    /deep/.wx-swiper-dot {
      width: 8rpx;
      height: 4rpx;
      background: rgba(0, 0, 0, 0.15);
    }
    /deep/.uni-swiper-dot-active,
    /deep/.wx-swiper-dot-active {
      width: 16rpx;
      background: var(--view-theme);
    }
    .page_swiper,
    swiper {
      height: 760rpx;
    }
  }
}
.product-con .footer {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  box-sizing: border-box;
  height: 100rpx;
  height: calc(100rpx+ constant(safe-area-inset-bottom)); ///兼容 IOS<11.2/
  height: calc(100rpx + env(safe-area-inset-bottom)); ///兼容 IOS>11.2/
  background-color: #fff;
  z-index: 277;
  border-top: 1px solid #f5f5f5;
  &.footpl {
    padding-left: 70rpx;
  }
}
.product-con .footer .item {
  font-size: 18rpx;
  color: #666;
  text-align: center;
}
.product-con .footer .item .iconfont {
  text-align: center;
  font-size: 40rpx;
}
.product-con .footer .item .iconfont.icon-ic_star1 {
  color: var(--view-priceColor);
}
.product-con .footer .item .iconfont.icon-ic_ShoppingCart {
  font-size: 40rpx;
  position: relative;
}
.product-con .footer .item .iconfont.icon-ic_ShoppingCart .num {
  color: #fff;
  position: absolute;
  font-size: 18rpx;
  height: 30rpx;
  border-radius: 200rpx;
  padding: 0 8rpx;
  box-sizing: border-box;
  top: -10rpx;
  right: -10rpx;
  background-color: var(--view-theme);
  min-width: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.product-con .footer .bnts.w_buy1 {
  width: 290rpx !important;
}
.product-con .footer .bnts.w_buy2 {
  width: 260rpx !important;
}
.product-con .footer .bnts {
  &.sold_out {
    width: 444rpx;
    border-radius: 50rpx;
  }
  &.virtual_buy {
    width: 444rpx;
    border-radius: 50rpx;
  }
  &.virtual_buy1,
  &.sold_out1 {
    width: 580rpx;
  }
  &.virtual_buy2,
  &.sold_out2 {
    width: 500rpx;
  }
  &.virtual_buy3,
  &.sold_out3 {
    width: 432rpx;
  }
}
.share-box {
  z-index: 1000;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  image {
    width: 100%;
    height: 100%;
  }
}
.right-wrapper {
  z-index: 99;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  .slideInRight {
    animation-duration: 0.5s;
  }
  .control-wrapper {
    z-index: 90;
    position: absolute;
    right: 0;
    top: 0;
    display: flex;
    flex-direction: column;
    width: 668rpx;
    height: 100%;
    background-color: #ffffff;
    border-radius: 40rpx 0 0 40rpx;
    .wrapper-count {
      height: calc(100% - 120rpx);
      overflow-y: auto;
    }
    .content-box {
      position: relative;
      display: flex;
      flex-direction: column;
      padding: 0 26rpx;
      .content-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 40rpx 0 20rpx;
        .title {
          font-size: 26rpx;
          font-weight: 500;
          color: #282828;
        }
        .btns {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 22rpx;
          color: #999;
          .iconfont {
            margin-left: 10rpx;
            margin-top: 5rpx;
            font-size: 20rpx;
          }
        }
      }
    }
    .brand-wrapper {
      flex: 1;
      overflow: hidden;
      .wrapper {
        display: flex;
        flex-wrap: wrap;
        padding-bottom: 20rpx;
      }
      .item {
        display: flex;
        width: 184rpx;
        height: 56rpx;
        align-items: center;
        justify-content: center;
        background: #f5f5f5;
        border-radius: 100rpx;
        margin-top: 25rpx;
        margin-right: 26rpx;
        font-size: 26rpx;
        &:nth-child(3n) {
          margin-right: 0;
        }
        &.on {
          background: var(--view-minorColor);
          border: 1px solid var(--view-theme);
          color: var(--view-theme);
        }
      }
      .btns {
        display: flex;
        align-items: center;
        justify-content: center;
        padding-top: 10rpx;
        font-size: 22rpx;
        color: #999;
        .iconfont {
          margin-left: 10rpx;
          margin-top: 5rpx;
          font-size: 20rpx;
        }
      }
    }
    .header {
      padding: 50rpx 26rpx 40rpx;
      .content-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .title {
        font-size: 26rpx;
        font-weight: 500;
        color: #282828;
      }
      .input-wrapper {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 28rpx;
        .input-placeholder {
          color: #999999;
          font-size: 26rpx;
        }
        input {
          width: 260rpx;
          height: 56rpx;
          padding: 0 10rpx;
          background: #f5f5f5;
          border-radius: 100rpx;
          font-size: 22rpx;
          text-align: center;
        }
        .line {
          width: 15rpx;
          height: 2rpx;
          background: #7d7d7d;
        }
      }
    }
  }
  .foot-btn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20rpx 30rpx;
    position: absolute;
    bottom: 0;
    width: 100%;
    .btn-item {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 290rpx;
      height: 68rpx;
      background: #ffffff;
      border: 1px solid var(--view-theme);
      border-radius: 34rpx;
      font-size: 26rpx;
      color: var(--view-theme);
      &.confirm {
        background: var(--view-theme);
        border-color: var(--view-theme);
        color: #fff;
      }
    }
  }
  .right-bg {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
  }
}
.carnum {
  height: 44rpx;
}
.carnum view {
  border: 1px solid #dddddd;
  height: 44rpx;
  font-size: 28rpx;
  color: #a4a4a4;
  display: flex;
  align-items: center;
  justify-content: center;
  .iconfont {
    font-size: 24rpx;
  }
}
.carnum .reduce {
  border-right: 0;
  width: 56rpx;
  border-radius: 22rpx 0 0 22rpx;
}
.carnum .plus {
  border-left: 0;
  width: 56rpx;
  border-radius: 0 22rpx 22rpx 0;
}
.carnum .reduce.on,
.carnum .plus.on {
  border-color: #e3e3e3;
  color: #dedede;
  pointer-events: none;
}
.carnum .num {
  color: #282828;
  min-width: 66rpx;
  width: 66rpx;
}
.product-window .carnum .uni-input-wrapper,
.product-window .carnum input {
  text-align: center;
}
.product-window .productWinList .item ~ .item {
  margin-top: 36rpx;
}
.product-window .productWinList .item .titles {
  font-size: 30rpx;
  color: #999;
  padding: 0 30rpx;
}
.product-window .productWinList .item .listn {
  padding: 0 30rpx 0 16rpx;
}
.product-window .productWinList .item .listn .itemn {
  border: 1px solid #f2f2f2;
  font-size: 24rpx;
  color: #282828;
  padding: 12rpx 20rpx;
  border-radius: 100rpx;
  margin: 20rpx 12rpx 0;
  background-color: #f2f2f2;
}
.product-window .productWinList .item .listn .itemn.on {
  color: var(--view-theme);
  background: var(--view-minorColor);
  border-color: var(--view-theme);
}
.detail-header {
  left: 0;
  top: 0;
  z-index: 999;
}
.head-wrapper {
  position: absolute;
  z-index: 10;
  left: 0;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20rpx;
  /* #ifdef MP */
  height: 43px;
  /* #endif */
  /* #ifdef H5 */
  height: 100rpx;
  /* #endif */
}
.head-wrapper .share-icon {
  width: 29px;
  height: 29px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 100%;
  background: rgba(255, 255, 255, 0.4);
  /* #ifdef MP */
  position: fixed;
  /* #endif */
}
.head-wrapper .share-icon .iconfont {
  color: #000;
  font-size: 34rpx;
}
.head-menu {
  display: flex;
  align-items: center;
  height: 54rpx;
  width: 140rpx;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 27rpx;
  .iconfont {
    flex: 1;
    text-align: center;
    color: #000;
    box-sizing: border-box;
    &.icon-ic_leftarrow {
      border-right: 1px solid rgba(0, 0, 0, 0.1);
    }
  }
}
.generate-posters {
  width: 100%;
  height: 170rpx;
  height: calc(170rpx+ constant(safe-area-inset-bottom)); ///兼容 IOS<11.2/
  height: calc(170rpx + env(safe-area-inset-bottom)); ///兼容 IOS>11.2/
  background-color: #fff;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 388;
  transform: translate3d(0, 100%, 0);
  transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
  border-radius: 24rpx 24rpx 0 0;
}
.generate-posters.on {
  transform: translate3d(0, 0, 0);
}
.generate-posters .item .iconfont {
  font-size: 36rpx;
  background-color: #5eae72;
  width: 70rpx;
  height: 70rpx;
  border-radius: 100%;
  color: #ffffff;
  display: inline-block;
  line-height: 70rpx;
  margin-bottom: 8rpx;
}
.generate-posters .item .iconfont.icon-a-ic_picture1 {
  background-color: #5391f1;
}
.generate-posters .item .iconfont.icon-ic_key {
  background-color: #fbb324;
}
.goodsStyle {
  background-color: #fff;
  padding: 22rpx 30rpx;
}
.goodsStyle .pictrue {
  width: 120rpx;
  height: 120rpx;
}
.goodsStyle .pictrue image {
  width: 100%;
  height: 100%;
  border-radius: 6rpx;
}
.goodsStyle .text {
  width: 520rpx;
  font-size: 28rpx;
  color: #999;
}
.goodsStyle .text .name {
  width: 340rpx;
  color: #282828;
}
.goodsStyle .text .money {
  text-align: right;
}
.goodsStyle .text .money .num {
  margin-top: 7rpx;
}
.goodWrapper .item {
  padding: 10rpx 30rpx 40rpx 30rpx;
  border-radius: 0 0 24rpx 24rpx;
  &:last-child {
    border-radius: 0;
  }
}
.goodWrapper .item .pictrue {
  width: 130rpx;
  height: 130rpx;
}
.goodWrapper .item .pictrue image {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
}
.goodWrapper .item .text {
  width: 500rpx;
  position: relative;
}
.goodWrapper .item .text .name {
  font-size: 28rpx;
  color: #282828;
  width: 453rpx;
}
.goodWrapper .item .text .num {
  font-size: 22rpx;
  color: #999;
  text-align: right;
}
.goodWrapper .item .text .attr {
  font-size: 20rpx;
  color: #999;
  margin-top: 16rpx;
}
.goodWrapper .item .text .money {
  font-size: 26rpx;
  margin-top: 17rpx;
}
.goodWrapper .item .text .evaluate {
  position: absolute;
  width: 114rpx;
  height: 46rpx;
  border: 1rpx solid #bbb;
  border-radius: 4rpx;
  text-align: center;
  line-height: 46rpx;
  right: 0;
  bottom: -5rpx;
}
.goodWrapper .item .text .evaluate.userEvaluated {
  font-size: 26rpx;
  color: #aaa;
  background-color: #f7f7f7;
  border-color: #f7f7f7;
}
.promoterHeader {
  width: 100%;
}
.promoterHeader .headerCon {
  width: 100%;
  height: 100%;
  padding: 0 88rpx 0 55rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #fff;
  background-image: url('data:image/png;base64,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');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.promoterHeader .headerCon .name {
  margin-bottom: 10rpx;
}
.promoterHeader .headerCon .num {
  font-size: 50rpx;
}
.promoterHeader .headerCon .iconfont {
  font-size: 125rpx;
}
.sign-record .list .item .data {
  height: 80rpx;
  line-height: 80rpx;
  padding: 0 30rpx;
  font-size: 24rpx;
  color: #666;
}
.sign-record .list .item .listn {
  font-size: 24rpx;
  color: #999;
  padding: 0 24rpx;
}
.sign-record .list .item .listn .itemn {
  height: 140rpx;
}
.sign-record .list:last-child .itemn {
  border: none;
}
.sign-record .list .item .listn .itemn .name {
  width: 390rpx;
  font-size: 28rpx;
  color: #282828;
  margin-bottom: 10rpx;
}
.sign-record .list .item .listn .itemn .num {
  font-size: 36rpx;
  color: #16ac57;
}
.coupon-list {
  padding: 0 30rpx;
  margin-top: 20rpx;
}
.coupon-list .item {
  width: 100%;
  height: 170rpx;
  margin-bottom: 16rpx;
}
.coupon-list .item .money {
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 240rpx;
  height: 100%;
  color: #fff;
  font-size: 28rpx;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.coupon-list .item .money.moneyGray {
  background-image: url('data:image/png;base64,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');
}
.coupon-list .item .money.vip-coupon {
  background-image: url('data:image/png;base64,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');
}
.coupon-list .item .money .num {
  font-size: 54rpx;
}
.coupon-list .item .money .pic-num {
  font-size: 20rpx;
}
.coupon-list .item .text {
  position: relative;
  overflow: hidden;
  width: 450rpx;
  padding: 0 17rpx 0 24rpx;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 0 24rpx 24rpx 0;
}
.coupon-list .item .text .condition {
  font-size: 30rpx;
  color: #282828;
  height: 93rpx;
  line-height: 93rpx;
  border-bottom: 1rpx solid #f5f5f5;
}
.coupon-list .item .text .data {
  font-size: 20rpx;
  color: #999;
  height: 76rpx;
}
.coupon-list .item .text .data .bnt {
  width: 136rpx;
  height: 52rpx;
  border-radius: 26rpx;
  font-size: 22rpx;
  text-align: center;
  line-height: 52rpx;
  color: #fff;
}
.coupon-list .item .text .data .gray {
  font-size: 120rpx;
  color: #dfdfdf;
  position: absolute;
  right: 0;
}
.coupon-list .item .text .data .gray.icon-ic_yilingqu {
  transform: rotateZ(-45deg);
}
.coupon-list .item .text .data .bnt1 {
  width: 110rpx;
  height: 48rpx;
  border-radius: 60rpx;
  font-size: 22rpx;
  text-align: center;
  line-height: 48rpx;
  color: #fff;
}
.coupon-list .item .text .data .bnt1.gray {
  background-color: #ccc;
}

.noCommodity {
  border-top: 7rpx solid #f5f5f5;
  text-align: center;
}
.noCommodity .pictrue {
  width: 414rpx;
  height: 305rpx;
  padding-bottom: 50rpx;
  margin: 0 auto;
}
.noCommodity .pictrue image {
  width: 414rpx;
  height: 305rpx;
}
.noCommodity .pictrue view {
  color: #999;
  font-size: 26rpx;
}
// 登录、注册、忘记密码
.register_main {
  width: 100%;
  height: 100vh;
  background-color: #ffffff;
  background-size: 100% auto;
  background-repeat: no-repeat;
}
.register {
  width: 100%;
  position: relative;
}
.register .shading .pictrue {
  width: 172rpx;
  height: 172rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  margin: 0 auto;
}
.register .shading .pictrue image {
  width: 164rpx;
  height: 164rpx;
  border-radius: 50%;
  display: block;
}
.register .whiteBg {
  width: 100%;
  margin: 0 auto;
  padding: 209rpx 72rpx 0;
}
.register .whiteBg .login_title .title_h {
  font-size: 48rpx;
  font-weight: bold;
}
.register .whiteBg .title_info {
  font-size: 28rpx;
  margin-top: 32rpx;
}
.register .whiteBg .list {
  margin-top: 106rpx;
}
.register .whiteBg .list .item {
  padding: 0 48rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 46rpx;
  position: relative;
  width: 100%;
  box-sizing: border-box;
}
.register .whiteBg .list .item + .item {
  margin-top: 32rpx;
}
.register .whiteBg .list .item .name {
  font-size: 26rpx;
  color: #2d3342;
  margin-bottom: 27rpx;
  text-align: left;
}
.register .whiteBg .list .item image {
  width: 40rpx;
  height: 40rpx;
  display: block;
}
.register .whiteBg .list .item input {
  font-size: 30rpx;
  flex: 1;
}
.register .whiteBg .list .item input::-webkit-input-placeholder {
  color: #ccc;
}
.register .whiteBg .list .item .placeholder {
  color: #cccccc;
  font-size: 28rpx;
}
.register .whiteBg .list .item .codeIput {
  width: 240rpx;
  border-right: 1px solid #cccccc;
}
.register .list .item .code {
  font-size: 28rpx;
  padding-left: 16px;
  background-color: transparent;
}
.register .whiteBg .tip text {
  color: #999999;
}
.register .whiteBg .list .forgetPwd {
  text-align: right;
  font-size: 28rpx;
  color: #666;
}
.register .whiteBg .list .forgetPwd .iconfont {
  font-size: 30rpx;
  margin-right: 10rpx;
  vertical-align: middle;
}
.register .whiteBg .tip {
  text-align: center;
  font-size: 28rpx;
  color: #999999;
  margin-top: 30rpx;
  .t-color {
    color: var(--view-theme);
  }
}
// 首页修改轮播内部样式；
.index-bg .uni-swiper-dot {
  width: 20rpx !important;
  height: 5rpx !important;
  border-radius: 3rpx;
}
.boutique .uni-swiper-dot {
  width: 7rpx !important;
  height: 7rpx !important;
  border-radius: 50%;
}
.boutique .uni-swiper-dot-active {
  width: 20rpx !important;
  border-radius: 5rpx !important;
}

.statistical-page .mc- {
  padding-bottom: 0;
}
.statistical-page .mpvue-calendar {
  min-width: 100%;
}
.statistical-page .mpvue-calendar table {
  margin: 0;
}
.statistical-page .mpvue-calendar td {
  border-right: 1px solid #fff;
  padding: 0;
  width: 14% !important;
}
.statistical-page .calendar-tools {
  box-shadow: unset;
  -webkit-box-shadow: unset;
  -o-box-shadow: unset;
  -moz-box-shadow: unset;
}
.statistical-page .mc-head-box div {
  font-size: 14px;
}
.statistical-page .mpvue-calendar td:not(.disabled) span.mc-date-red {
  color: unset;
}
.statistical-page .mpvue-calendar .mc-range-mode .mc-range-begin span.calendar-date,
.statistical-page .mpvue-calendar .mc-range-mode .mc-range-end span.calendar-date {
  border-radius: 0;
  background-color: #2291f8 !important;
}
.statistical-page .mpvue-calendar td.selected span.mc-date-red {
  color: #fff;
}
.statistical-page .mc-range-mode .selected .mc-range-bg {
  background-color: #a0dcf9;
}
.statistical-page .mpvue-calendar .mc-range-mode .mc-range-row-first .calendar-date,
.statistical-page .mpvue-calendar .mc-range-mode .mc-range-row-last .calendar-date {
  background-color: #a0dcf9;
}
.statistical-page .mpvue-calendar .mc-range-mode .selected.mc-range-second-to-last span {
  background-color: #a0dcf9;
}
.statistical-page .mpvue-calendar .mc-range-mode .mc-range-month-first.selected .calendar-date,
.statistical-page .mpvue-calendar .mc-range-mode .mc-range-month-last.selected .calendar-date {
  background-color: #a0dcf9;
}
.statistical-page .mc-today-element .calendar-date {
  border-radius: 0;
  background-color: unset;
}
.new-users .uni-swiper-dot {
  width: 8px;
  height: 4px;
  background: rgba(0, 0, 0, 0.15);
  border-radius: 2px;
}
.new-users .uni-swiper-dot-active {
  width: 16px;
  height: 4px;
  background: rgba(233, 51, 35, 1) !important;
  border-radius: 2px;
}
.pictrue_log {
  width: 80rpx;
  height: 40rpx;
  border-radius: 20rpx 0 20rpx 0;
  line-height: 40rpx;
  font-size: 24rpx;
}
.pictrue_log_class {
  background: -webkit-gradient(
    linear,
    left top,
    right top,
    from(rgba(246, 122, 56, 1)),
    to(rgba(241, 27, 9, 1))
  );
  background: linear-gradient(90deg, rgba(246, 122, 56, 1) 0, rgba(241, 27, 9, 1) 100%);
  opacity: 1;
  position: absolute;
  top: 0;
  left: 0;
  color: #fff;
  text-align: center;
  z-index: 3;
}
.pictrue_log_medium {
  width: 80rpx;
  height: 44rpx;
  border-radius: 20rpx 0 20rpx 0;
  line-height: 44rpx;
  text-align: center;
  font-size: 26rpx;
}
.pictrue_log_big {
  width: 100rpx;
  height: 46rpx;
  line-height: 46rpx;
  border-radius: 20rpx 0 20rpx 0;
  font-size: 28rpx;
}
.product-con .nav .time .styleAll {
  padding: 0 6rpx;
  font-size: 22rpx;
  // background-color: #fff;
  border-radius: 2rpx;
}
.spike-box .styleAll {
  background-color: #ffdfdd;
  color: #e93323;
  padding: 0 5rpx;
}
.product-con .nav .time .timeTxt {
  color: #fff;
}
.bg-color-hui {
  background: #bbb !important;
}
/deep/.page_content .swiper .uni-swiper-dot-active {
  background: #e93323;
}
.pictrue_log_xl {
  background: linear-gradient(90deg, rgba(246, 122, 56, 1) 0, rgba(241, 27, 9, 1) 100%);
}
.pictrue_log_xl_gray {
  background: linear-gradient(90deg, rgba(102, 102, 102, 1) 0, rgba(153, 153, 153, 1) 100%);
}
.pictrue_log_xl_blue {
  background: linear-gradient(90deg, rgba(26, 163, 246, 1) 0, rgba(24, 192, 244, 1) 100%);
}
.flex-aj-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.page-index.bgf .noCommodity {
  border-top: 0;
}
.product-con .red {
  color: #82848f;
}
.pl-20 {
  padding-left: 20rpx;
}

.mask_transparent {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 300;
}
.confirmImg {
  width: 100%;
}
.confirmImg .upload {
  padding-bottom: 36rpx;
}
.confirmImg .upload .pictrue {
  margin: 22rpx 23rpx 0 0;
  width: 146rpx;
  height: 146rpx;
  position: relative;
  font-size: 24rpx;
  color: #999;
}
.confirmImg .upload .pictrue:nth-of-type(4n) {
  margin-right: 0;
}
.confirmImg .upload .pictrue image {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
}
.confirmImg .upload .pictrue .icon-ic_close2 {
  position: absolute;
  font-size: 45rpx;
  top: -10rpx;
  right: -10rpx;
}
.confirmImg .upload .pictrue .icon-ic_camera1 {
  font-size: 50rpx;
  margin-bottom: 10rpx;
}
.confirmImg .upload .pictrue .close {
  position: absolute;
  width: 26rpx;
  height: 26rpx;
  border-radius: 0 8rpx 0 8rpx;
  background-color: rgba(0, 0, 0, 0.6);
  top: 0;
  right: 0;
}
.confirmImg .upload .pictrue .close .iconfont {
  font-size: 24rpx;
}
.confirmImg .upload .pictrue:nth-last-child(1) {
  border: 2rpx solid #ddd;
  box-sizing: border-box;
  border-radius: 16rpx;
}
.conter .copy {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 76rpx;
  height: 34rpx;
  border: 2rpx solid #ddd;
  border-radius: 26rpx;
  font-size: 20rpx;
  color: #666;
  margin-left: 6rpx;
}
.btn-lg {
  width: 690rpx;
  height: 88rpx;
  margin: 0 auto;
  line-height: 88rpx;
  text-align: center;
  color: #fff;
  background: var(--view-theme);
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: bold;
}
.placeholder {
  color: #999999;
}
.placeholderc {
  color: #cccccc;
}
.popup-close {
  display: inline-block;
  width: 36rpx;
  height: 36rpx;
  border-radius: 100%;
  background: #eeeeee;
  line-height: 36rpx;
  text-align: center;
  color: #999;
  font-size: 26rpx;
  font-weight: normal;
}
.start {
  width: 122rpx;
  height: 30rpx;
  background-image: url('data:image/png;base64,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');
  background-repeat: no-repeat;
  background-size: 122rpx auto;
}
.start.star5 {
  background-position: 0 3rpx;
}
.start.star4 {
  background-position: 0 -30rpx;
}
.start.star3 {
  background-position: 0 -70rpx;
}
.start.star2 {
  background-position: 0 -105rpx;
}
.start.star1 {
  background-position: 0 -140rpx;
}
.start.star0 {
  background-position: 0 -175rpx;
}
.loadingpic {
  animation: load 3s linear 1s infinite;
  --webkit-animation: load 3s linear 1s infinite;
}
.loading-list {
  animation: load linear 1s infinite;
  -webkit-animation: load linear 1s infinite;
  font-size: 40rpx;
  margin-right: 22rpx;
}
.loading {
  width: 100%;
  height: 100rpx;
  line-height: 100rpx;
  align-items: center;
  justify-content: center;
  position: relative;
  text-align: center;
}
.loading .line {
  position: absolute;
  width: 450rpx;
  left: 150rpx;
  top: 50rpx;
  height: 1px;
  border-top: 1px solid #eee;
}
.loading .text {
  position: relative;
  display: inline-block;
  padding: 0 20rpx;
  background: #fff;
  z-index: 2;
  color: #777;
}
.loadingicon .loading {
  animation: load linear 1s infinite;
  font-size: 45rpx;
}
.loadingicon {
  width: 100%;
  height: 80rpx;
  overflow: hidden;
  color: #ccc;
}
.ChangePassword .list {
  padding: 0 30rpx;
}
.ChangePassword .list .item {
  width: 100%;
  height: 100rpx;
  display: flex;
  align-items: center;
  border-bottom: 2rpx solid #f2f2f2;
  &:last-child {
    border: none;
  }
}
.ChangePassword .list .item input {
  width: 100%;
  height: 100%;
  font-size: 30rpx;
}
.ChangePassword .list .item .placeholder {
  color: #cccccc;
}
.ChangePassword .list .item input.codeIput {
  width: 340rpx;
}
.ChangePassword .list .item .code {
  font-size: 30rpx;
  background-color: #fff;
  color: var(--view-theme);
}
.ChangePassword .list .item .code.on {
  color: #cccccc;
}
.ChangePassword .confirmBnt {
  font-size: 28rpx;
  width: 100%;
  height: 88rpx;
  border-radius: 50rpx;
  color: #fff;
  margin-top: 60rpx;
  text-align: center;
  line-height: 88rpx;
  background-color: var(--view-theme);
}
.payment .item .left .icon {
  width: 52rpx;
  height: 52rpx;
  border-radius: 10rpx;
  background: linear-gradient(360deg, #43c93e 0%, #4be146 100%);
}
.payment .item .left .iconfont {
  font-size: 36rpx;
  color: #ffffff;
}
.payment .item .left .icons-icon-a-ic_alipay,
.payment .item .left .icons-icon-ic_Money2 {
  background: linear-gradient(360deg, #1281ff 0%, #70b3ff 100%);
}
.payment .item .left .icons-icon-a-ic_offlinepay {
  background: linear-gradient(180deg, rgba(255, 162, 47, 0.8) 0%, #ff7b00 100%);
}
.add-cart,
.wf-item .add-cart {
  width: 48rpx;
  height: 48rpx;
  border-radius: 100%;
  background: var(--view-theme);
  position: absolute;
  right: 30rpx;
  bottom: 0;
  .iconfont {
    color: #fff;
    font-size: 30rpx;
  }
}
.wf-item .add-cart {
  bottom: 20rpx;
  right: 20rpx;
}
.sys-head {
  position: relative;
  width: 100%;
  text-align: center;
  top: 0;
  left: 0;
  &.fixed {
    position: fixed;
    z-index: 99;
  }
  .bg {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-size: 100% auto;
    background-position: left bottom;
  }
  .sys-title {
    z-index: 10;
    position: relative;
    height: 43px;
    line-height: 43px;
    font-size: 32rpx;
    color: #ffffff;
  }
  .icon-ic_leftarrow {
    position: fixed;
    z-index: 10;
    color: #fff;
    left: 20rpx;
    font-size: 40rpx;
  }
}
.fixed-head {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 10;
  .tool-bar {
    display: flex;
    align-items: center;
    height: 40px;
  }
  .icon-ic_leftarrow {
    margin-right: 40rpx;
    margin-left: 20rpx;
    font-size: 40rpx;
    color: #fff;
  }
}

.score-wrapper {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
  .item {
    color: #999999;
    font-size: 26rpx;
    text {
      margin-left: 10rpx;
      color: var(--view-priceColor);
    }
  }
}
.priceChange {
  position: fixed;
  width: 580upx;
  top: 50%;
  left: 50%;
  margin-left: -290upx;
  margin-top: -335upx;
  z-index: 666;
  transition: all 0.3s ease-in-out 0s;
  transform: scale(0);
  opacity: 0;
}
.priceChange.on {
  opacity: 1;
  transform: scale(1);
}
.change-count {
  border-radius: 16rpx 16rpx 24rpx 24rpx;
  padding-bottom: 40rpx;
}
.priceChange .priceTitle {
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 100%;
  height: 160upx;
  border-radius: 10upx 10upx 0 0;
  text-align: center;
  font-size: 40upx;
  color: #fff;
  line-height: 160upx;
  position: relative;
}
.priceChange .close {
  text-align: center;
  margin-top: 50rpx;
}
.priceChange .icon-ic_close1 {
  font-size: 50upx;
  color: #ffffff;
  display: inline-block;
}
.priceChange .listChange {
  padding: 0 40upx;
}
.priceChange .listChange textarea {
  box-sizing: border-box;
}
.priceChange .listChange .item {
  height: 103upx;
  border-bottom: 1px solid #e3e3e3;
  font-size: 32upx;
  color: #333;
}
.priceChange .modify {
  font-size: 28upx;
  color: #fff;
  width: 440upx;
  height: 88upx;
  text-align: center;
  line-height: 88upx;
  border-radius: 50upx;
  background-color: #2291f8;
  margin: 28upx auto 0;
}
.priceChange .listChange textarea {
  border: 1px solid #eee;
  width: 100%;
  height: 200upx;
  margin-top: 50upx;
  border-radius: 10upx;
  color: #333;
  padding: 20upx;
}
/deep/ .page-footer .uni-badge--x {
  position: absolute !important;
  top: -8rpx;
}
/deep/ .page-footer .uni-badge {
  right: unset !important;
  top: unset !important;
}
/deep/ .page-footer .uni-badge-left-margin .uni-badge--error {
  color: #fff !important;
  background-color: #e93323 !important;
  z-index: 8;
}
.instructions {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 100;
}
.instructions .agreement-count {
  width: 656rpx;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  text-align: center;
}
.instructions .setAgCount {
  background: #fff;
  border-radius: 12rpx;
  -webkit-border-radius: 12rpx;
  padding: 52rpx 0;
  overflow: hidden;
  .content {
    /deep/ p {
      font-size: 13px;
      line-height: 22px;
    }
    /deep/ img {
      max-width: 100%;
    }
  }
}
.instructions .icon {
  display: inline-block;
  font-size: 42rpx;
  color: #ffffff;
  margin-top: 40rpx;
}
.instructions .setAgCount .title {
  color: #333;
  font-size: 32rpx;
  text-align: center;
  font-weight: bold;
}
.instructions .setAgCount .agBox {
  margin-top: 32rpx;
  color: #333;
  font-size: 26rpx;
  line-height: 22px;
  text-align: justify;
  text-justify: distribute-all-lines;
  height: 756rpx;
  padding: 0 52rpx;
  overflow-y: scroll;
}
.center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.btnHoverClass::after {
  background-color: rgba(0, 0, 0, 0.08) !important;
}
.button-hover::after {
  background-color: rgba(0, 0, 0, 0.08) !important;
}

.ios-pb {
  padding-bottom: calc(constant(safe-area-inset-bottom) + 28rpx) !important;
  padding-bottom: calc(env(safe-area-inset-bottom) + 28rpx) !important;
}

