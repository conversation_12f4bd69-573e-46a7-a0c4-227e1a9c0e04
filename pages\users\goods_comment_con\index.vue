<template>
	<view class="pad20" :style="viewColor">
		<form @submit="formSubmit" report-submit='true'>
			<view class='evaluate-con bg-f boder-24' v-if="productInfo.product">
				<view class='goodsStyle acea-row row-between mt20 bg-f boder-24'>
					<view class='pictrue'>
						<image :src='productInfo.productAttr.image' v-if="productInfo.productAttr.image"></image>
						<image :src='productInfo.product.image' v-else></image>
					</view>
					<view class='text acea-row row-between'>
						<view class="name">
							<view class='line2'>{{productInfo.product.store_name}}</view>
							<view class="sku">{{productInfo.productAttr.sku}}</view>
						</view>
						
						<view class='money'>
							<view>¥{{ productInfo.product_type == 3 ? productInfo.productAssistAttr.assist_price : productInfo.productAttr.price}}</view>
							<view class='num'>x{{cart_num}}</view>
						</view>
					</view>
				</view>
				<view class='score'>
					<view class='item acea-row row-middle' v-for="(item,indexw) in scoreList" :key="indexw">
						<view>{{item.name}}</view>
						<view class='starsList'>
							<text @click="stars(indexn, indexw)" v-for="(itemn, indexn) in item.stars" :key="indexn" class='iconfont' :class="item.index >= indexn? 'icon-ic_star1 font-color':'icon-ic_star'"></text>
						</view>
						<text class='evaluate'>{{item.index === -1 ? "" : item.index + 1 + "分"}}</text>
					</view>
					<view class='textarea'>
						<textarea placeholder='商品满足你的期待么？说说你的想法，分享给想买的他们吧~' name="comment" placeholder-class='placeholder'></textarea>
						<view class='list acea-row row-middle'>
							<view class='pictrue' v-for="(item,index) in pics" :key="index">
								<image :src='item'></image>
								<text class='iconfont icon-ic_close2 font-color' @click='DelPic(index)'></text>
							</view>
							<view class='pictrue acea-row row-center-wrapper row-column' @click='uploadpic' v-if="pics.length < 6">
								<text class='iconfont icon-ic_camera1'></text>
								<view>上传图片</view>
							</view>
						</view>
					</view>
					<button class='evaluateBnt' formType="submit">立即评价</button>
				</view>
			</view>
		</form>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import { orderProduct,orderComment} from '@/api/order.js';
	import { mapGetters } from "vuex";
	import { toLogin } from '@/libs/login.js';
	export default {
		components: {},
		data() {
			return {
				pics: [],
				scoreList: [{
						name: "商品质量",
						stars: ["", "", "", "", ""],
						index: -1
					},
					{
						name: "服务态度",
						stars: ["", "", "", "", ""],
						index: -1
					},
					{
						name: "物流服务",
						stars: ["", "", "", "", ""],
						index: -1
					}
				],
				orderId: '',
				unique: '',
				productInfo: {},
				cart_num: 0,
			};
		},
		computed: mapGetters(['isLogin','viewColor']),
		onLoad(options) {
			if (!options.uni && options.order_id) return this.$util.Tips({
				title: '缺少参数'
			}, {
				tab: 3,
				url: 1
			});
			this.unique = options.uni;
			this.orderId = options.order_id;
			if (this.isLogin) {
				this.getOrderProduct();
			} else {
				toLogin()
			}
		},
		methods: {
			/**
			 * 获取某个产品详情
			 *
			 */
			getOrderProduct: function() {
				let that = this;
				orderProduct(that.unique).then(res => {
					that.$set(that, 'productInfo', res.data.cart_info);
					that.cart_num = res.data.product_num;
				}).catch(error=>{
					this.$util.Tips({
						title:error
					},{
						tab: 3,
						url: 1
					})
				})
			},
			stars: function(indexn, indexw) {
				this.scoreList[indexw].index = indexn;
			},
			/**
			 * 删除图片
			 *
			 */
			DelPic: function(index) {
				let that = this,
					pic = this.pics[index];
				that.pics.splice(index, 1);
				that.$set(that, 'pics', that.pics);
			},

			/**
			 * 上传文件
			 *
			 */
			uploadpic: function() {
				let that = this;
				that.$util.uploadImageOne('upload/image', function(res) {
					console.log(res);
					that.pics.push(res.data.path);
					that.$set(that, 'pics', that.pics);
				});
			},
			/**
			 * 立即评价
			 */
			formSubmit: function(e) {
				let formId = e.detail.formId,
					value = e.detail.value,
					that = this,
					product_score = that.scoreList[0].index + 1 === 0 ? "" : that.scoreList[0].index + 1,
					service_score = that.scoreList[1].index + 1 === 0 ? "" : that.scoreList[1].index + 1,
					logistics_score = that.scoreList[2].index + 1 === 0 ? "" : that.scoreList[2].index + 1;
				if (!value.comment) return that.$util.Tips({
					title: '请填写你对宝贝的心得！'
				});
				value.product_score = product_score;
				value.service_score = service_score;
				value.postage_score = logistics_score;
				value.pics = that.pics;
				uni.showLoading({
					title: "正在发布评论……"
				});
				orderComment(this.unique,value).then(res => {
					uni.hideLoading();
					that.$util.Tips({
						title: '感谢您的评价!',
						icon: 'success'
					});
					setTimeout(()=>{
						uni.redirectTo({url:'/pages/order_details/index?order_id=' + that.orderId})
					},500)
				}).catch(err => {
					uni.hideLoading();
					return that.$util.Tips({
						title: err
					});
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.evaluate-con .score {
		border-top: 1px solid #f5f5f5;
		font-size: 28rpx;
		color: #282828;
		padding: 48rpx 30rpx 65rpx 30rpx;
	}
	.evaluate-con .score .item~.item {
		margin-top: 30rpx;
	}
	.evaluate-con .sku {
		color: #999999;
		font-size: 20rpx;
		margin-top: 16rpx;
	}
	.evaluate-con .score .item .starsList {
		padding: 0 35rpx 0 40rpx;
	}
	.evaluate-con .score .item .starsList .iconfont {
		font-size: 40rpx;
		color: #aaa;
	}
	.evaluate-con .score .item .starsList .iconfont~.iconfont {
		margin-left: 20rpx;
	}
	.evaluate-con .score .item .evaluate {
		color: #aaa;
		font-size: 24rpx;
	}
	.evaluate-con .score .textarea {
		width: 100%;
		background-color: #fafafa;
		border-radius: 10rpx;
		margin-top: 48rpx;
	}
	.evaluate-con .score .textarea textarea {
		font-size: 28rpx;
		padding: 38rpx 30rpx 0 30rpx;
		width: 100%;
		box-sizing: border-box;
		height: 160rpx;
	}
	.evaluate-con .score .textarea .placeholder {
		color: #bbb;
	}
	.evaluate-con .score .textarea .list {
		margin-top: 25rpx;
		padding-left: 5rpx;
	}
	.evaluate-con .score .textarea .list .pictrue {
		width: 140rpx;
		height: 140rpx;
		margin: 0 0 35rpx 25rpx;
		position: relative;
		font-size: 22rpx;
		color: #bbb;
		border-radius: 16rpx;
	}
	.evaluate-con .score .textarea .list .pictrue:nth-last-child(1) {
		border: 1px solid #ddd;
		box-sizing: border-box;
	}
	.evaluate-con .score .textarea .list .pictrue image {
		width: 100%;
		height: 100%;
		border-radius: 16rpx;
	}
	.evaluate-con .score .textarea .list .pictrue .icon-ic_close2 {
		font-size: 45rpx;
		position: absolute;
		top: -20rpx;
		right: -20rpx;
	}
	.evaluate-con .score .textarea .list .pictrue .icon-ic_camera1 {
		color: #bfbfbf;
		font-size: 50rpx;
	}
	.evaluate-con .score .evaluateBnt {
		font-size: 28rpx;
		color: #fff;
		width: 100%;
		height: 88rpx;
		border-radius: 50rpx;
		text-align: center;
		line-height: 86rpx;
		margin-top: 45rpx;
		background-color: var(--view-theme);
	}
</style>
