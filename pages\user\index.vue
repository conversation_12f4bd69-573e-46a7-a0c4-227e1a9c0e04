<template>
  <view :style="viewColor">
    <skeleton
      :show="showSkeleton"
      :isNodes="isNodes"
      ref="skeleton"
      loading="chiaroscuro"
      selector="skeleton"
      bgcolor="transparent"
    ></skeleton>
    <!-- 自定义顶部背景颜色 -->
    <view class="top">
      <!-- #ifdef MP || APP-PLUS -->
      <view class="sys-head">
        <view class="sys-bar" :style="{ height: sysHeight }"></view>
        <!-- #ifdef MP -->
        <view class="sys-title">我的</view>
        <!-- #endif -->
        <view class="bg"></view>
      </view>
      <!-- #endif -->
    </view>
    <!-- 自定义顶部背景颜色 -->
    <view class="new-users skeleton" :style="{ visibility: showSkeleton ? 'hidden' : 'visible' }">
      <view class="head">
        <view class="user-card" :class="svip_switch_status == 1 ? 'svip-card' : ''">
          <view class="bg"></view>
          <view class="user-info">
            <view
              class="avatar-box"
              :class="{ on: userInfo.is_svip > 0 && svip_switch_status == 1 }"
            >
              <image
                class="avatar skeleton-radius"
                :src="userInfo.avatar ? userInfo.avatar : '/static/images/f.png'"
                @click="goEdit"
              ></image>
              <view class="headwear" v-if="userInfo.is_svip > 0 && svip_switch_status == 1">
                <image :src="`${domain}/static/images/headwear.png`" />
              </view>
            </view>
            <view class="info">
              <!--#ifdef MP-->
              <view class="name" v-if="!userInfo.uid" @click="openAuto">请点击授权</view>
              <!--#endif-->
              <!--#ifdef APP-PLUS-->
              <view class="name" v-if="!userInfo.uid" @click="openAuto">请点击登录</view>
              <!--#endif-->
              <!--#ifdef H5-->
              <view class="name" v-if="!userInfo.uid" @click="openAuto">
                <text v-if="isWeixin">请点击授权</text>
                <text v-else>请点击登录</text>
              </view>
              <!--#endif-->
              <view class="name" v-if="userInfo.uid">
                {{ userInfo.nickname }}
              </view>
              <view class="num" v-if="userInfo.uid">
                <view class="num-txt">ID：{{ userInfo.uid }}</view>
              </view>
            </view>
            <view class="edit center" @click="goEdit">
              <image src="/static/images/icon-bianji.png" class="icon" mode="scaleToFill" />
              编辑资料
            </view>
          </view>
          <view class="num-wrapper skeleton-rect">
            <view class="num-item" @click="goMenuPage('/pages/users/user_goods_collection/index')">
              <text class="num regular">
                {{ userInfo.now_money || 0 }}
              </text>
              <view class="txt">余额</view>
            </view>
            <view
              v-if="hide_mer_status != 1"
              class="num-item"
              @click="goMenuPage('/pages/users/user_goods_collection/index?tab=2')"
            >
              <text class="num regular">{{ userInfo.integral ? userInfo.integral : 0 }}</text>
              <view class="txt">御享值</view>
            </view>
            <view class="num-item" @click="goMenuPage('/pages/users/user_coupon/index')">
              <text class="num regular">{{ userInfo.total_coupon || 0 }}</text>
              <view class="txt">优惠券</view>
            </view>
          </view>
          <view class="vip-card-wrap">
            <view class="vip-card">
              <view class="vip-card-body">
                <view class="vip-card__left">
                  <view class="label">当前会员等级</view>
                  <view class="desc">
                    <view class="level-name">
                      {{ userInfo.phone ? memberInfo.member.brokerage_name : '未注册' }}
                    </view>
                    <view class="progress-bar" v-if="userInfo.phone">
                      <view class="progress-bar__desc">
                        成长值{{ memberInfo.member_value }}/{{
                          memberInfo.next_level.brokerage_rule.value
                        }}
                      </view>
                      <view class="progress-bar__box">
                        <view class="line" :style="{ width: nextVipProgress + '%' }"></view>
                      </view>
                    </view>
                  </view>
                </view>
                <view class="vip-card__right" v-if="userInfo.phone">
                  <text class="tip">升级银卡享更多权益</text>
                  <button hover-class="button-hover" class="register-btn" @click="goSvip">
                    查看升级
                  </button>
                </view>
                <view class="vip-card__right" v-else>
                  <text class="tip">注册会员享更多权益</text>
                  <button hover-class="button-hover" class="register-btn" @click="goRegister">
                    立即注册
                  </button>
                </view>
              </view>

              <image src="/static/images/icon-level-5.png" class="level" mode="scaleToFill" />
              <image src="/static/images/icon-mask.png" class="icon-mask" mode="scaleToFill" />
            </view>
          </view>
        </view>
      </view>
      <view class="wrapper">
        <view class="order-wrapper">
          <view class="order-hd flex skeleton-rect">
            <view class="left">我的订单</view>
            <view
              class="right flex"
              @click="authTo('/pages/users/order_list/index?status=-1')"
              hover-class="none"
              open-type="navigate"
            >
              查看全部
              <text class="iconfont icon-ic_rightarrow"></text>
            </view>
          </view>
          <view class="order-bd">
            <block v-for="(item, index) in orderMenu" :key="index">
              <view class="order-item" @click="authTo(item.url)" hover-class="none">
                <view class="pic">
                  <!-- <text class="iconfont" :class="item.icon"></text> -->
                  <image :src="item.src" class="icon-img" mode="wdithFix" />
                  <text class="order-status-num" v-if="item.num > 0">
                    {{ item.num }}
                  </text>
                </view>
                <view class="txt skeleton-rect">{{ item.title }}</view>
              </view>
            </block>
          </view>
        </view>
        <!-- 轮播 -->
        <view class="slider-wrapper skeleton-rect" v-if="imgUrls.length > 0">
          <swiper
            indicator-dots="true"
            :autoplay="autoplay"
            :circular="circular"
            :interval="interval"
            :duration="duration"
            indicator-color="rgba(255,255,255,0.6)"
            indicator-active-color="#fff"
          >
            <block v-for="(item, index) in imgUrls" :key="index">
              <swiper-item>
                <view @click="goUrl(item.url)" class="slide-navigator acea-row row-between-wrapper">
                  <image :src="item.pic" class="slide-image"></image>
                </view>
              </swiper-item>
            </block>
          </swiper>
        </view>
        <!-- 会员菜单 -->
        <view class="user-menus" style="margin-top: 20rpx">
          <view class="title skeleton-rect">我的服务</view>
          <view class="menu-box">
            <block v-for="(item, index) in personalMenu" :key="index">
              <view v-if="item.isShow" class="item">
                <view @click="authTo(item.url)" class="item-count">
                  <image v-if="showSkeleton" class="skeleton_image skeleton-radius"></image>
                  <image v-else :src="item.pic"></image>
                  <text>{{ item.name }}</text>
                </view>
              </view>
            </block>
          </view>
        </view>
        <!-- 链接 -->
        <view class="service-nav">
          <view
            class="nav-item"
            v-for="(item, index) in serviceMenu"
            :key="index"
            @click="authTo(item.url)"
          >
            <view class="item-left">
              <image :src="item.icon" class="icon" mode="scaleToFill" />
              <view class="title">{{ item.title }}</view>
            </view>
            <text class="iconfont icon-ic_rightarrow"></text>
          </view>
        </view>
        <view
          class="menus-list-item"
          v-if="!userInfo.topService && userInfo.service"
          @click="toService(0)"
        >
          <view class="item-text">
            <view class="title">
              <text>商家</text>
              管理
            </view>
            <view class="info merchant">
              <text>进入管理中心</text>
              <text class="iconfont icon-ic_rightarrow"></text>
            </view>
          </view>
          <view
            class="image merchant-image"
            :style="{
              'background-image': `url(${domain}/static/images/merchant-image.png)`,
            }"
          ></view>
        </view>
        <view
          class="menus-list-item"
          v-if="!userInfo.service && userInfo.topService"
          @click="toService(1)"
        >
          <view class="item-text">
            <view class="title">
              <text>平台</text>
              管理
            </view>
            <view class="info plantform">
              <text>进入管理中心</text>
              <text class="iconfont icon-ic_rightarrow"></text>
            </view>
          </view>
          <view
            class="image plantform-image"
            :style="{
              'background-image': `url(${domain}/static/images/plantform-image.png)`,
            }"
          ></view>
        </view>
        <view class="menu-list-count" v-if="userInfo.topService && userInfo.service">
          <view class="menu-item" @click="toService(0)">
            <view
              class="image merchant-image"
              :style="{
                'background-image': `url(${domain}/static/images/merchant-image.png)`,
              }"
            ></view>
            <view class="menu-text">
              <view class="title">
                <text class="merchant">商家</text>
                管理
              </view>
              <view class="info merchant">
                <text>进入管理中心</text>
                <text class="iconfont icon-ic_rightarrow"></text>
              </view>
            </view>
          </view>
          <view class="menu-item" @click="toService(1)">
            <view
              class="image plantform-image"
              :style="{
                'background-image': `url(${domain}/static/images/plantform-image.png)`,
              }"
            ></view>
            <view class="menu-text">
              <view class="title">
                <text class="plantform">平台</text>
                管理
              </view>
              <view class="info plantform">
                <text>进入管理中心</text>
                <text class="iconfont icon-ic_rightarrow"></text>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view v-if="copyright.status !== -1" class="copy-right">
        <image class="img-copyright" :src="copyright.image" mode="widthFix"></image>
        <view class="text">{{ copyright.Copyright }}</view>
      </view>
      <view v-else class="copy-right">
        <view class="iconfont icon-crmeb"></view>
        <view class="text">Copyright ©{{ new Date().getFullYear() }} CRMEB. All Rights</view>
      </view>
      <view style="height: 80rpx"></view>
      <!-- #ifndef H5 -->
      <passwordPopup></passwordPopup>
      <!-- #endif -->
    </view>
    <!-- 登录弹窗 -->
    <LoginPopup ref="loginRef" @closeAttr="closeAttr" />
    <!--自定义底部tab栏-->
    <customTab :newData="newData" :activeRouter="activeRouter"></customTab>
  </view>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
let sysHeight = uni.getSystemInfoSync().statusBarHeight + 'px'
import { getMenuList, getUserInfo, setVisit, memberInfo } from '@/api/user.js'
import { getVersion, getNavigation } from '@/api/public'
import { orderData } from '@/api/order.js'
import { mapGetters } from 'vuex'
import dayjs from '@/plugin/dayjs/dayjs.min.js'
import Cache from '@/utils/cache'
// #ifndef H5
import passwordPopup from '@/components/passwordPopup'
// #endif
import customTab from '@/components/customTab'
import { configMap, getCustomer } from '@/utils'
import Auth from '../../libs/wechat'
import { HTTP_REQUEST_URL } from '@/config/app'
import { toLogin } from '@/libs/login.js'
import LoginPopup from '@/components/loginPopup/loginPopup.vue'
const app = getApp()
export default {
  components: {
    // #ifndef H5
    passwordPopup,
    // #endif
    customTab,
    LoginPopup,
  },
  computed: configMap(
    {
      mer_intention_open: 0,
      hide_mer_status: 0,
      recharge_switch: 0,
      integral_status: 0,
      member_status: 0,
      balance_func_status: 0,
      svip_switch_status: 0,
      navigation: {},
      service_type: {},
    },
    mapGetters(['isLogin', 'viewColor', 'keyColor', 'cartNum']),
  ),
  filters: {
    dateFormat: function (value) {
      return dayjs(value * 1000).format('YYYY-MM-DD')
    },
  },
  data() {
    return {
      //#ifdef H5
      isWeixin: this.$wechat.isWeixin(),
      //#endif
      domain: HTTP_REQUEST_URL,
      showSkeleton: true, //骨架屏显示隐藏
      isNodes: 0, //控制什么时候开始抓取元素节点,只要数值改变就重新抓取
      orderMenu: [
        {
          icon: 'icon-ic_daifukuan12',
          src: '/static/images/icon-user-daizhifu.png',
          title: '待支付',
          url: '/pages/users/order_list/index?status=0',
          num: 0,
        },
        {
          icon: 'icon-ic_daifahuo11',
          src: '/static/images/icon-user-daishiyong.png',
          title: '待使用',
          url: '/pages/users/order_list/index?status=1',
          num: 0,
        },
        {
          icon: 'icon-ic_daishouhuo1',
          src: '/static/images/icon-user-yiwancheng.png',
          title: '已完成',
          url: '/pages/users/order_list/index?status=2',
          num: 0,
        },
        {
          icon: 'icon-ic_daipingjia1',
          src: '/static/images/icon-user-daipingjia.png',
          title: '待评价',
          url: '/pages/users/order_list/index?status=3',
          num: 0,
        },
        {
          icon: 'icon-ic_daituikuan1',
          src: '/static/images/icon-user-tuiluan.png',
          title: '售后/退款',
          url: '/pages/users/refund/list',
          num: 0,
        },
      ],
      imgUrls: [{ url: '', pic: '' }],
      userMenu: [],
      skeletonMenu: [],
      personalMenu: [
        { pic: '', name: '', isShow: true },
        { pic: '', name: '', isShow: true },
        { pic: '', name: '', isShow: true },
        { pic: '', name: '', isShow: true },
        { pic: '', name: '', isShow: true },
        { pic: '', name: '', isShow: true },
        { pic: '', name: '', isShow: true },
        { pic: '', name: '', isShow: true },
        { pic: '', name: '', isShow: true },
        { pic: '', name: '', isShow: true },
        { pic: '', name: '', isShow: true },
        { pic: '', name: '', isShow: true },
      ],
      autoplay: true,
      circular: true,
      interval: 3000,
      duration: 500,
      orderStatusNum: {},
      userInfo: { aratar: '/static/f.png', is_svip: 0 },
      MyMenus: [],
      is_promoter: 0, //推广人开关  1开
      extension_status: 0,
      copyright: { copyright_status: 1 },
      newData: {},
      activeRouter: '',
      sysHeight: sysHeight,
      show_promoter: false,
      serviceMenu: [
        {
          icon: '/static/images/icon-recruit.png',
          title: '招聘',
          url: '/pages/news_details/index?id=5',
        },
        {
          icon: '/static/images/icon-cooperation.png',
          title: '商务合作',
          url: '/pages/news_details/index?id=6',
        },
        {
          icon: '/static/images/icon-clear.png',
          title: '了解清享',
          url: '/pages/news_details/index?id=7',
        },
        {
          icon: '/static/images/icon-points.png',
          title: '如何获取御享值',
          url: '/pages/news_details/index?id=8',
        },
      ],
      memberInfo: {}, // 会员信息
      nextVipProgress: 0, // 下个会员等级进度
    }
  },
  onLoad() {},
  onReady() {
    this.isNodes++
  },
  mounted: function () {
    this.getVersion()
  },
  onShow: function () {
    let that = this
    let routes = getCurrentPages()
    let curRoute = routes[routes.length - 1].route
    that.activeRouter = '/' + curRoute
    that.getNav()
    that.$util.getCartNum(that.isLogin, null)
    if (that.isLogin) {
      that.getUserInfo()
      that.orderNum()
      that.getMemberInfo()
    } else {
      that.orderMenu.forEach((v) => {
        v.num = 0
      })
      that.getMyMenus()
    }
    setTimeout(() => {
      that.showSkeleton = false
    }, 500)
  },
  methods: {
    authTo(url) {
      if (this.isLogin) {
        uni.navigateTo({ url: url })
      } else {
        this.openAuto()
      }
    },

    goSvip() {
      if (this.isLogin) {
        if (this.userInfo.is_svip > 0) {
          uni.navigateTo({ url: '/pages/annex/vip_center/index' })
        } else {
          uni.navigateTo({ url: '/pages/annex/vip_paid/index' })
        }
      } else {
        this.openAuto()
      }
    },
    goRouter(item) {
      var pages = getCurrentPages()
      var page = pages[pages.length - 1].$page.fullPath
      if (item.link == page) return
      uni.switchTab({
        url: item.link,
        fail(err) {
          uni.redirectTo({ url: item.link })
        },
      })
    },
    getNav() {
      getNavigation().then((res) => {
        this.newData = res.data
        if (this.newData.status && this.newData.status.status) {
          uni.hideTabBar()
        } else {
          uni.showTabBar()
        }
      })
    },
    toService(is_sys) {
      uni.navigateTo({ url: '/pages/admin/business/index?is_sys=' + is_sys })
    },
    getVersion() {
      getVersion().then((data) => {
        this.copyright = data.data
      })
    },
    // 菜单显示
    filterMenus: function (item) {
      let that = this
      if (item.url == '/pages/users/user_money/index') item.isShow = that.balance_func_status == 1
      else if (item.url == '/pages/users/user_spread_user/index') {
        if (that.extension_status == 0) {
          item.isShow = false
        } else if (that.extension_status == 1) {
          item.isShow = that.show_promoter
          if (that.is_promoter != 1) item.url = '/pages/users/distributor/index'
        }
      } else if (item.url == '/pages/store/settled/index')
        item.isShow = that.mer_intention_open == 1
      else if (item.url == '/pages/users/user_grade/index') item.isShow = that.member_status == 1
      else if (item.url == '/pages/users/user_integral/index')
        item.isShow = that.integral_status == 1
      else item.isShow = true
    },
    showMenu(menu) {
      return (
        !menu ||
        [
          'integral',
          'service',
          'admin_order',
          'verify_order',
          'intention',
          'promoter',
          'balance',
        ].indexOf(menu) === -1
      )
    },
    goUrl(url) {
      if (url.indexOf('http') != -1) {
        // #ifdef H5
        location.href = url
        // #endif
      } else {
        if (
          [
            '/pages/goods_cate/goods_cate',
            '/pages/order_addcart/order_addcart',
            '/pages/user/index',
            '/pages/plant_grass/index',
          ].indexOf(url) == -1
        ) {
          uni.navigateTo({ url: url })
        } else {
          uni.switchTab({ url: url })
        }
      }
    },
    // 去聊天列表
    goChat() {
      let type = this.userInfo.service ? 1 : 0
      uni.navigateTo({ url: `/pages/chat/customer_list/index?type=${type}` })
    },
    // 记录会员访问
    setVisit() {
      setVisit({ url: '/pages/user/index' }).then((res) => {})
    },
    // 打开授权
    openAuto() {
      // toLogin()
      this.$refs.loginRef.openPopup()
    },
    Setting: function () {
      uni.openSetting({ success: function (res) {} })
    },
    // 绑定手机
    bindPhone() {
      uni.navigateTo({ url: '/pages/users/user_phone/index' })
    },
    /**
     * 获取个人用户信息
     */
    getUserInfo: function () {
      let that = this
      getUserInfo().then((res) => {
        that.userInfo = res.data
        that.is_promoter = res.data.promoter && res.data.promoter.isPromoter
        that.show_promoter = res.data.promoter && res.data.promoter.isShow
        that.extension_status = res.data.promoter.extension_status
        that.getMyMenus()
      })
    },
    closeAttr() {
      this.getUserInfo()
    },
    // 订单数字
    orderNum() {
      orderData().then(({ data }) => {
        this.orderMenu.forEach((item, index) => {
          switch (item.title) {
            case '待付款':
              item.num = data.noPay
              break
            case '待发货':
              item.num = data.noPostage
              break
            case '待收货':
              item.num = data.noDeliver
              break
            case '待评价':
              item.num = data.noComment
              break
            case '售后/退款':
              item.num = data.refund
              break
          }
        })
      })
    },
    /**
     *
     * 获取个人中心图标
     */
    getMyMenus: function () {
      let that = this
      if (this.MyMenus.length) return
      getMenuList().then((res) => {
        that.personalMenu = []
        res.data.menu.forEach((item, index) => {
          that.filterMenus(item)
          that.personalMenu.push(item)
        })
        that.imgUrls = res.data.banner
        uni.stopPullDownRefresh() //结束下拉刷新
      })
    },
    // 编辑页面
    goEdit() {
      if (this.isLogin) {
        // uni.navigateTo({ url: '/pages/users/user_info/index' })
        uni.navigateTo({ url: '/pages/hairdressing/profile/index' })
      } else {
        this.openAuto()
      }
    },
    // 去注册会员
    goRegister() {
      if (this.isLogin) {
        uni.navigateTo({ url: '/pages/hairdressing/member_registration/index' })
      } else {
        this.openAuto()
      }
    },
    // 去会员中心
    goSvip() {
      uni.navigateTo({ url: '/pages/hairdressing/member_center/index' })
    },
    goSetting() {
      if (this.isLogin) {
        uni.navigateTo({ url: '/pages/users/user_setting/index' })
      } else {
        this.openAuto()
      }
    },
    // 签到
    goSignIn() {
      uni.navigateTo({ url: '/pages/users/user_sgin/index' })
    },
    goMenuPage(url) {
      if (this.isLogin) {
        uni.navigateTo({ url })
      } else {
        this.openAuto()
      }
    },
    // 获取会员信息
    async getMemberInfo() {
      const result = await memberInfo()
      console.log('🚀 ~ getMemberInfo ~ result:', result)
      if (result.status == 200) {
        this.memberInfo = result.data
        this.nextVipProgress = (
          (result.data.member_value / result.data.next_level.brokerage_rule.value) *
          100
        ).toFixed(2)
      } else {
        this.$util.Tips({ title: result.msg })
      }
    },
  },
  onPullDownRefresh: function () {
    this.getNav()
    if (this.isLogin) {
      this.getUserInfo()
      this.orderNum()
      this.getMemberInfo()
    } else {
      this.userInfo = {}
      this.orderMenu.forEach((v) => {
        v.num = 0
      })
      this.getMyMenus()
    }
  },
}
</script>

<style lang="scss" scoped>
.cardVipA {
  position: absolute;
  background: linear-gradient(145deg, #f8e3a8 0%, #e8c077 100%);
  background-size: 100% 100%;
  width: 710rpx;
  height: 84rpx;
  bottom: 0;
  left: 20rpx;
  padding: 0 30rpx 0 105rpx;
  border-radius: 24rpx 24rpx 0 0;
  box-sizing: border-box;
  .svip_user {
    width: 52rpx;
    height: 52rpx;
    border-radius: 100%;
    position: absolute;
    left: 30rpx;
    top: 17rpx;
  }
  .left-box {
    font-size: 26rpx;
    color: #905100;
    font-weight: 400;
  }
  .btn {
    color: #905100;
    font-weight: 400;
    font-size: 24rpx;
  }
  .btn-open {
    background: #282828;
    border-radius: 40rpx;
    color: #f7e1a6;
    font-size: 24rpx;
    width: 140rpx;
    height: 50rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.new-users {
  .head {
    background: #fff;
    .user-card {
      position: relative;
      width: 100%;
      padding: 46rpx 0 50rpx;
      // background-image: linear-gradient(90deg, var(--view-bntColor21) 0%, var(--view-bntColor22) 100%);
      background: linear-gradient(180deg, #fde7d1 0%, #fafafa 100%);
      &.svip-card {
        padding: 35rpx 0 0;
      }
      .bg {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-size: 100% 100%;
      }
      .user-info {
        z-index: 20;
        position: relative;
        display: flex;
        align-items: center;
        padding: 0 28rpx;
        .level_icon {
          width: 34rpx;
          height: 32rpx;
          margin: 4rpx 0 0 6rpx;
          border: none;
        }
        .avatar-box {
          width: 100rpx;
          height: 100rpx;
          border-radius: 50%;
          position: relative;
          .avatar,
          image {
            width: 100rpx;
            height: 100rpx;
            border-radius: 50%;
          }
          &.on {
            .avatar {
              border: 2px solid #ffac65;
              border-radius: 50%;
              box-sizing: border-box;
            }
          }
        }
        .headwear {
          position: absolute;
          right: -4rpx;
          top: -14rpx;
          width: 44rpx;
          height: 44rpx;
          z-index: -1;
          image {
            width: 100%;
            height: 100%;
          }
        }
        .info {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          margin-left: 20rpx;
          padding: 15rpx 0;
          .name {
            display: flex;
            align-items: center;
            color: #333333;
            font-size: 32rpx;
            .vip {
              width: 82rpx;
              height: 36rpx;
              margin-left: 12rpx;
              image {
                width: 82rpx;
                height: 36rpx;
              }
            }
          }
          .num {
            display: flex;
            align-items: center;
            font-size: 26rpx;
            color: #666666;
            image {
              width: 22rpx;
              height: 23rpx;
              margin-left: 20rpx;
            }
          }
        }
        .edit {
          font-weight: 400;
          font-size: 24rpx;
          color: #999999;
          .icon {
            width: 22rpx;
            height: 22rpx;
            margin-right: 10rpx;
          }
        }
      }
      .num-wrapper {
        z-index: 30;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 40rpx 20rpx 0;
        color: #fff;
        .num-item {
          width: 25%;
          text-align: center;
          .num {
            font-size: 42rpx;
            font-weight: 500;
            color: #333333;
          }
          .txt {
            margin-top: 20rpx;
            font-size: 22rpx;
            color: #999999;
          }
        }
      }
      .sign {
        z-index: 200;
        position: absolute;
        right: -12rpx;
        top: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 120rpx;
        height: 60rpx;
        background: linear-gradient(90deg, rgba(255, 225, 87, 1) 0%, rgba(238, 193, 15, 1) 100%);
        border-radius: 29rpx 4rpx 4rpx 29rpx;
        color: #282828;
        font-size: 28rpx;
        font-weight: bold;
      }
    }
  }
  .wrapper {
    position: relative;
    padding: 0 20rpx;
    margin-top: 20rpx;
  }
  .order-wrapper {
    background-color: #fff;
    border-radius: 24rpx;
    .order-hd {
      height: 100rpx;
      align-items: center;
      justify-content: space-between;
      padding: 0 30rpx;
      color: #282828;
      .left {
        font-weight: 500;
        font-size: 30rpx;
        color: #131414;
      }
      .right {
        align-items: center;
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
        .icon-ic_rightarrow {
          margin-left: 5rpx;
          font-size: 26rpx;
        }
      }
    }
    .order-bd {
      display: flex;
      .order-item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 20%;
        height: 160rpx;
        .pic {
          position: relative;
          text-align: center;
          .iconfont {
            font-size: 48rpx;
            // color: var(--view-theme);
          }
          .icon-img {
            width: 42rpx;
            height: 42rpx;
          }
        }
        .txt {
          margin-top: 8rpx;
          font-size: 26rpx;
          color: #282828;
        }
      }
    }
  }
  .slider-wrapper {
    margin: 20rpx 0;
    height: 130rpx;
    border-radius: 24rpx;
    swiper,
    swiper-item {
      height: 100%;
    }
    image {
      width: 100%;
      height: 130rpx;
      border-radius: 24rpx;
    }
  }
  .user-menus {
    padding-bottom: 30rpx;
    background-color: #fff;
    border-radius: 24rpx;
    .title {
      padding: 40rpx 30rpx;
      font-weight: 500;
      font-size: 30rpx;
      color: #131414;
    }
    .item {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      text-align: center;
      .item-count {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
      }
      image {
        width: 70rpx;
        height: 70rpx;
      }
      .skeleton_image {
        width: 80rpx;
        height: 80rpx;
      }
      text {
        margin-top: 10rpx;
        font-size: 26rpx;
        color: #999999;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        width: 78%;
      }
      &:last-child::before {
        display: none;
      }
    }
    button {
      font-size: 28rpx;
    }
  }
  .phone {
    color: #666666;
  }
  .order-status-num {
    background-color: #fff;
    color: var(--view-theme);
    border-radius: 200rpx;
    position: absolute;
    right: -12rpx;
    top: -12rpx;
    font-size: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 30rpx;
    height: 30rpx;
    padding: 0 8px;
    box-sizing: border-box;
    border: 1px solid var(--view-theme);
  }
}
.sys-head {
  .bg {
    background-color: #fde7d1;
    // background-image:linear-gradient(90deg, var(--view-bntColor21) 0%, var(--view-bntColor22) 100%);
  }
  .sys-title {
    font-size: 32rpx;
    color: #333;
  }
}
.menus-list-item {
  background: #fff;
  border-radius: 24rpx;
  padding: 30rpx 50rpx 30rpx 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20rpx;
  .title {
    font-size: 34rpx;
    color: #282828;
    font-weight: bold;
  }
  .info {
    margin-top: 15rpx;
    font-size: 22rpx;
    display: flex;
    align-items: center;
    .iconfont {
      font-size: 20rpx;
      margin-left: 4rpx;
    }
    &.merchant {
      color: #e93323;
    }
    &.plantform {
      color: #ffc552;
    }
  }
  .image {
    width: 180rpx;
    height: 108rpx;
    background-size: 100%;
    background-repeat: no-repeat;
  }
}
.copy-right {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #cccccc;
  font-size: 22rpx;
  margin-top: 40rpx;
  margin-bottom: 0;
  .iconfont {
    font-size: 60rpx;
  }
  .img-copyright {
    width: 120rpx;
    height: 60rpx;
  }
}
.menu-box {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40rpx 30rpx;
  padding: 0 40rpx;
}
.right-btn {
  z-index: 99;
  position: absolute;
  right: 30rpx;
  top: 40rpx;
  display: flex;
  align-items: center;
  color: #fff;
  .iconfont {
    font-size: 40rpx;
    margin-left: 33rpx;
  }
  .btn {
    position: relative;
  }
  .iconnum {
    min-width: 6px;
    background-color: #fff;
    color: var(--view-theme);
    border-radius: 15rpx;
    position: absolute;
    right: -10rpx;
    top: -10rpx;
    font-size: 10px;
    padding: 0 4px;
  }
}
.menu-list-count {
  margin-top: 20rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .menu-item {
    width: 345rpx;
    border-radius: 24rpx;
    padding: 20rpx;
    background-color: #fff;
    display: flex;
    align-items: center;
    position: relative;
    .image {
      width: 106rpx;
      height: 64rpx;
      background-size: 100%;
      background-repeat: no-repeat;
      &.merchant-image {
        margin-right: 15rpx;
      }
      &.plantform-image {
        margin-left: 15rpx;
      }
    }
    .menu-text {
      margin-left: 10rpx;
      .title {
        color: #282828;
        font-weight: bold;
        font-size: 26rpx;
      }
      .info {
        display: flex;
        align-items: center;
        font-size: 22rpx;
        margin-top: 16rpx;
        .iconfont {
          font-size: 20rpx;
          margin-left: 4rpx;
        }
        &.merchant {
          color: #e93323;
        }
        &.plantform {
          color: #ffc552;
        }
      }
    }
  }
}
.vip-card-wrap {
  padding: 0 30rpx;
  margin-top: 20rpx;
  .vip-card {
    padding: 24rpx 20rpx;
    background: linear-gradient(107deg, #f4e8d7 0%, #efcfb6 100%);
    border-radius: 18rpx;
    position: relative;
    height: 126rpx;
    box-sizing: border-box;
    .vip-card-body {
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      z-index: 2;
    }
    &__left {
      .label {
        font-weight: 300;
        font-size: 18rpx;
        color: #805d3e;
      }
      .desc {
        display: flex;
        align-items: center;
        gap: 20rpx;
        .level-name {
          font-weight: 600;
          font-size: 30rpx;
          color: #999999;
          background: linear-gradient(39deg, #805d3e 0%, #b0815e 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .progress-bar {
          &__desc {
            font-weight: 300;
            font-size: 18rpx;
            color: #805d3e;
          }
          &__box {
            width: 168rpx;
            height: 6rpx;
            background: #efd1b8;
            border-radius: 3rpx;
          }
          .line {
            // width: 84rpx;
            height: 6rpx;
            background: #b0815e;
            border-radius: 3rpx;
          }
        }
      }
    }
    &__right {
      display: flex;
      align-items: center;
      .tip {
        font-weight: 300;
        font-size: 18rpx;
        color: #805d3e;
      }
      .register-btn {
        font-weight: 300;
        font-size: 20rpx;
        color: #ffffff;
        padding: 10rpx 16rpx;
        background: #b0815e;
        border-radius: 19rpx;
        margin-left: 20rpx;
      }
    }
    .icon-mask {
      width: 100%;
      height: 24rpx;
      position: absolute;
      left: 0;
      bottom: 0;
      z-index: 2;
    }
    .level {
      width: 86rpx;
      height: 120rpx;
      position: absolute;
      top: 0;
      right: 6rpx;
      z-index: 1;
    }
  }
}
.service-nav {
  background: #fff;
  border-radius: 24rpx;
  padding: 20rpx 30rpx;
  margin-top: 20rpx;

  .nav-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx 0;
    border-bottom: 1px solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    .item-left {
      display: flex;
      align-items: center;

      .icon {
        width: 34rpx;
        height: 34rpx;
        margin-right: 20rpx;
      }

      .title {
        font-size: 28rpx;
        color: #333;
      }
    }

    .icon-ic_rightarrow {
      font-size: 24rpx;
      color: #999;
    }
  }
}
</style>
