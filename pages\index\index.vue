<template>
	<view :class="bgTabVal==2?'fullsize noRepeat':bgTabVal==1?'repeat ysize':'noRepeat ysize'"
		:style="'background-color:'+bgColor+';background-image: url('+bgPic+');min-height:'+windowHeight+'px;'" id="home">
		<skeleton :show="showSkeleton" :isNodes="isNodes" ref="skeleton" loading="chiaroscuro" selector="skeleton" bgcolor="transparent"></skeleton>
		<view v-if="!errorNetwork" :style="{visibility: showSkeleton ? 'hidden' : 'visible', pointerEvents: preview ? 'none' : 'auto'}">
			<view class="page-index skeleton" :class="{ bgf: navIndex > 0 }">
				<block>
					<!-- #ifdef H5 -->
					<homeComb v-if="smallPage" class="home-comb" :isScale="isScale" :isMenu="isMenu" :dataConfig="homeCombData" :userInfo="userInfo" :isFixed="isFixed" :isScrolled="isScrolled" @changeDiy="changeDiy"></homeComb>
					<view v-for="(item, index) in styleConfig" :key="index">
						<block v-if="item.name != 'headerSerch' && item.name != 'tabNav' && item.name != 'shopList' && item.name != 'homeComb' && item.name != 'hotRanking' && item.name != 'pageFoot'">
							<component
								:is="item.name"
								:index="index"
								:isFixed="isFixed"
								:dataConfig="item"
								:tempArr="tempArr"
								:userInfo="userInfo"
								:tabTitle="navTop"
								:navIndex="navIndex"
								@changeTab="changeTab"
								@detail="goDetail"
								v-show="navIndex == 0"
							></component>
						</block>
						<block v-if="item.name == 'headerSerch'">
							<headerSerch
								:dataConfig="item"
								:tempArr="tempArr"
								:userInfo="userInfo"
								:tabTitle="navTop"
								@changeTab="changeTab"
								@detail="goDetail"
							></headerSerch>
						</block>
						<block v-if="item.name == 'shopList' && hide_mer_status != 1 && navIndex == 0">
							<shopList :dataConfig="item"></shopList>
						</block>
						<block v-if="item.name == 'tabNav'">
							<tabNav
								:isFixed="isFixed"
								:dataConfig="item"
								:tempArr="tempArr"
								:userInfo="userInfo"
								:tabTitle="navTop"
								@changeTab="changeTab"
								@detail="goDetail"
							></tabNav>
						</block>
						<block v-if="item.name == 'homeComb' && !smallPage">
							<homeComb :isMenu="isMenu" :dataConfig="item" :isFixed="isFixed" :isScrolled="isScrolled" :userInfo="userInfo" @changeDiy="changeDiy"></homeComb>
						</block>
						<block v-if="item.name == 'hotRanking' && hot_ranking_switch != 0 && navIndex == 0">
							<hotRanking :dataConfig="item"></hotRanking>
						</block>
					</view>				
					<!-- #endif -->
					<!-- #ifdef MP || APP-PLUS -->
					<homeComb class="home-comb" v-if="smallPage" :isScale="isScale" :isMenu="isMenu" :dataConfig="homeCombData" :isFixed="isFixed" :isScrolled="isScrolled" :userInfo="userInfo" @changeDiy="changeDiy"></homeComb>
					<block v-for="(item, index) in styleConfig" :key="index" >
						<view v-show="navIndex == 0">
							<homeComb v-if="item.name == 'homeComb' && !smallPage" :isMenu="isMenu" :dataConfig="item" :isFixed="isFixed" :isScrolled="isScrolled" :userInfo="userInfo" @changeDiy="changeDiy"></homeComb>
							<activeParty v-if="item.name == 'activeParty'" :dataConfig="item"></activeParty>
							<bargain v-if="item.name == 'bargain'" :dataConfig="item"></bargain>
							<blankPage v-if="item.name == 'blankPage'" :dataConfig="item"></blankPage>
							<combination v-if="item.name == 'combination'" :dataConfig="item"></combination>
							<topic v-if="item.name == 'topic'" :dataConfig="item"></topic>
							<coupon v-if="item.name == 'coupon'" :dataConfig="item"></coupon>
							<customerService v-if="item.name == 'customerService'" :dataConfig="item"></customerService>
							<goodList v-if="item.name == 'goodList'" :dataConfig="item" :isCustom="isFooter" @detail="goDetail"></goodList>
							<guide v-if="item.name == 'guide'" :dataConfig="item"></guide>
							<liveBroadcast v-if="item.name == 'liveBroadcast'" :dataConfig="item"></liveBroadcast>
							<menus v-if="item.name == 'menus'" :class="isMenu ? 'transform-view' : ''" :dataConfig="item"></menus>
							<news v-if="item.name == 'news'" :dataConfig="item"></news>
							<presellList v-if="item.name == 'presellList'" :dataConfig="item"></presellList>
							<plantList v-if="item.name == 'plantList'" :dataConfig="item"></plantList>
							<hotRanking v-if="item.name == 'hotRanking' && hot_ranking_switch != 0" :dataConfig="item"></hotRanking>
							<promotionList v-if="item.name == 'promotionList'" :dataConfig="item" @changeTab="changeTab" :tempArr="tempArr" @detail="goDetail"></promotionList>
							<richText v-if="item.name == 'richText'" :dataConfig="item"></richText>
							<seckill v-if="item.name == 'seckill'" :dataConfig="item"></seckill>
							<swiperBg v-if="item.name == 'swiperBg'" :dataConfig="item"></swiperBg>
							<pictureCube v-if="item.name == 'pictureCube'" :dataConfig="item"></pictureCube>
							<titles v-if="item.name == 'titles'" :dataConfig="item"></titles>
							<shopList v-if="item.name == 'shopList' && hide_mer_status != 1" :dataConfig="item"></shopList>
						</view>
						<headerSerch v-if="item.name == 'headerSerch'" :dataConfig="item" :userInfo="userInfo"></headerSerch>
						<tabNav v-if="item.name == 'tabNav'" :dataConfig="item" class="tabNav" :tabTitle="navTop"
						@changeTab="changeTab" :isFixed="isFixed"></tabNav>
					</block>
					<!-- #endif -->
					<view class="main" v-show="navIndex == 0">
						<!-- 首页推荐 -->
						<view v-if="recommend_switch == 1" class="index-product-wrapper">
							<!-- 首发新品 -->
							<recommend 
							:hostProduct="hostProduct" 
							:indexP="true" 
							:isTab="true" 
							:isLogin="isLogin" 
							:isCustom="isFooter">
							</recommend>
							<view class="loadingicon acea-row row-center-wrapper" v-if="hostProduct.length > 0">
								<text class="loading iconfont icon-jiazai" :hidden="hotLoading == false"></text>
								{{ hotTitle }}
							</view>
						</view>
					</view>
					<view v-show="navIndex == 0" class="loadingicon acea-row row-center-wrapper" v-if="tempArr.length && styleConfig[styleConfig.length - 1].name == 'promotionList'">
						<text class="loading iconfont icon-jiazai" :hidden="loading == false"></text>
						{{ loadTitle }}
					</view>
				</block>
				<!-- 分类页 -->
				<view class="productList" v-if="navIndex > 0" :style="viewColor">
					<view class="sort acea-row" v-if="sortList.length>0"  :style="{ marginTop: sortMarTop + 'px' }">
						<navigator
							hover-class="none"
							v-if="index < 9"
							:url="'/pages/columnGoods/goods_list/index?id=' + item.store_category_id + '&title=' + item.cate_name"
							class="item"
							v-for="(item, index) in sortList"
							:key="item.store_category_id"
						>
							<view class="pictrue cate_pictrue">
								<easy-loadimage :image-src="item.pic"></easy-loadimage>
								<view v-if="item.border_pic" :style="{ backgroundImage: `url(${item.border_pic})` }" class="border-picture"></view>
							</view>
							<view class="text">{{ item.cate_name }}</view>
						</navigator>
						<view class="item" @click="bindMore" v-if="sortList.length >= 9">
							<view class="pictrues cate_pictrue acea-row row-center-wrapper"><text class="iconfont icon-ic_more"></text></view>
							<view class="text">更多</view>
						</view>
					</view>					
					<block v-if="sortProduct.length > 0">
						<view class="list acea-row row-between-wrapper">
							<navigator @tap="goDetails(item)" class="item" v-for="(item, index) in sortProduct" :key="item.product_id">
								<view class="pictrue">
									<easy-loadimage :image-src="item.image"></easy-loadimage>
									<view v-if="item.border_pic" :style="{ backgroundImage: `url(${item.border_pic})` }" class="border-picture"></view>
								</view>
								<view class="text">
									<view class="name line1">
										<text class="name_text line2">{{ item.store_name }}</text>
									</view>
									<view class="acea-row row-middle">
										<view class="money">
											<priceFormat :price="item.price" weight intSize="40" floatSize="26" labelSize="26"></priceFormat>
										</view>
									</view>
									<view class="item_tags acea-row">
										<text v-if="item.merchant.type_name && item.product_type == 0" class="font-bg-red">{{ item.merchant.type_name }}</text>
										<text v-else-if="item.merchant.is_trader && item.product_type == 0" class="font-bg-red">自营</text>
										<text v-if="item.product_type != 0" :class="'font_bg-red type' + item.product_type">
											{{ item.product_type == 1 ? '秒杀' : item.product_type == 2 ? '预售' : item.product_type == 3 ? '助力' : item.product_type == 4 ? '拼团' : '' }}
										</text>
										<text class="tags_item ticket" v-if="item.issetCoupon">领券</text>
										<text class="tags_item delivery" v-if="item.delivery_free == 1">包邮</text>
									</view>
								</view>
								<view class="add-cart acea-row row-middle row-center">
									<text class="iconfont icon-ic_ShoppingCart1"></text>
								</view>
							</navigator>
							<view class="loadingicon acea-row row-center-wrapper" v-if="sortProduct.length > 0 || sortProductLoading">
								<text class="loading iconfont icon-jiazai" :hidden="loading == false"></text>
								{{ loadTitle }}
							</view>
						</view>
					</block>		
					<block v-if="sortProduct.length == 0">
						<view class="noCommodity">
							<view class="pictrue" style="margin: 0 auto;">
								<image :src="`${domain}/static/images/no_thing.png`"></image>
								<view>暂无商品，去看点什么吧</view>
							</view>
							<recommend :hostProduct="hostProduct" :isTab="true"></recommend>
						</view>
					</block>
				</view>
				<!-- #ifdef H5 -->
				<a v-if="beian_sn" href="https://beian.miit.gov.cn" class="copyRight">{{beian_sn}}</a>
				<!-- #endif -->
				<!-- #ifndef H5 -->
				<view v-if="beian_sn" class="copyRight" @click="goCopyright('https://beian.miit.gov.cn')">{{beian_sn}}</view>
				<!-- #endif -->
			</view>
		</view>
		<view v-else>
			<view class="error-network">
				<image :src="`${domain}/static/images/error-network.png`"></image>
				<view class="title">网络连接断开</view>
				<view class="con">
					<view class="label">请检查情况：</view>
					<view class="item">· 在设置中是否已开启网络权限</view>
					<view class="item">· 当前是否处于弱网环境</view>
					<view class="item">· 版本是否过低，升级试试吧</view>
				</view>
				<view class="btn" @click="reconnect">重新连接</view>
			</view>
		</view>
		<!-- #ifdef APP-PLUS -->
		<view class="privacy-wrapper" v-if="privacyStatus">
			<view class="privacy-box">
				<view class="title">用户协议与隐私政策</view>
				<view class="content">
					请务必审慎阅读、充分理解“用户协议 与 隐私政策”各条款，包括但不限于：为了 向你提供即时通讯、内容分享等服务，我 们需要收集你的设备信息、操作日志等个
					人信息。你可以在“设置”中查看、变更、 删除个人信息并管理你的授权。
					<br />
					你可以阅读
					<navigator url="/pages/users/user_about/index?from=sys_user_agree">《用户协议》</navigator>与<navigator url="/pages/users/user_about/index?from=sys_userr_privacy">《隐私政策》</navigator>
					了解 详细信息。如你同意，请点击“我同意”开始接受我们的服务。
				</view>
				<view class="btn-box">
					<view class="btn-item" @click="confirmApp">我同意</view>
					<view class="btn" @click="closeModel">随便逛逛</view>
				</view>
			</view>
		</view>
		<!-- #endif -->
		<!-- 发送给朋友图片 -->
		<view class="share-box" v-if="isIntegral">
			<!-- #ifndef H5 -->
			<image :src="`${domain}/static/images/share-info.png`" @click="closeShare"></image>
			<!-- #endif -->
			<!-- #ifdef H5 -->
			<image :src="`${domain}/static/images/share-info-wechat.png`" @click="closeShare"></image>
			<!-- #endif -->
		</view>
		<!-- 优惠券弹窗 -->
		<view class="coupon_popups" v-if="showCoupon">
		<!-- <view class="coupon_popups"> -->
			<view class="bg"></view>
			<view class="con" @click.stop="goCoupon" :style="{'background-image': register_popup_pic ? `url(${register_popup_pic})` :  `url(${domain}/static/images/coupon-window.png)`}">
				<scroll-view v-if="!register_popup_pic" scroll-y="true" class="coupon-count">
					<view class="item" v-for="(item, index) in couponArray" :style="{'background-image': `url(${domain}/static/images/coupon-list-bg.png)`}">
						<view class="left">
							<view class="price semiBold">
								<priceFormat :price="item.coupon_price" weight intSize="40" floatSize="26" labelSize="24"></priceFormat>
							</view>
							<block v-if="item.coupon_type != 'balance'">
								<view class="max_price" v-if="item.use_min_price > 0">满{{item.use_min_price}}可用</view>
								<view class="max_price" v-else>领券立减{{item.coupon_price}}元</view>
							</block>	
						</view>
						<view class="right">
							<view class="title line1">{{item.coupon_type=='balance' ? "新人专享余额" : "新人专享优惠券"}}~</view>
							<view class="info line1">{{item.coupon_type=='balance' ? "余额" : "优惠券"}}已放入您的账户中~</view>
						</view>
					</view>
				</scroll-view>
				<view v-if="!register_popup_pic" class="integral-count">
					福利已放入您的账户，还有{{register_give_integral}}积分，快去下单啦！
				</view>
				<view class="coupon-window-btn">
					<block v-if="!register_popup_pic">	
						<navigator v-if="!only_register_money" url="/pages/columnGoods/goods_coupon_list/index" hover-class="none" class="coupon-btn" :style="{ 'background-image': `url(${domain}/static/images/coupon-btn.png)` }"></navigator>
						<navigator v-else url="/pages/columnGoods/goods_list/index" hover-class="none" class="coupon-btn" :style="{ 'background-image': `url(${domain}/static/images/coupon-btn.png)` }"></navigator>
					</block>	
					<view class='iconfont icon-ic_close1' @click.stop="showCoupon = false"></view>
				</view>
			</view>
		</view>
		<!-- #ifndef H5 -->
		<passwordPopup></passwordPopup>
		<!-- #endif -->
		<!--自定义底部tab栏-->
		<customTab :newData="newData" :activeRouter="activeRouter"></customTab>
	</view>
</template>
<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
var statusBarHeight = uni.getSystemInfoSync().statusBarHeight + 'px';
let app = getApp();
import { getUserInfo } from '@/api/user.js';
import { getDiy, getPageDiy, getIndexData, getAppVersion } from '@/api/api.js';
import { getNavigation } from '@/api/public.js';
import { getStorage } from '@/libs/uniApi.js';
import Cache from '@/utils/cache';
// #ifdef MP-WEIXIN
import { getTemlIds } from '@/api/api.js';
import { SUBSCRIBE_MESSAGE, TIPS_KEY } from '@/config/cache';
// #endif
import { getShare } from '@/api/public.js';
import { goShopDetail } from '@/libs/order.js';
import { toLogin } from '@/libs/login.js';
import { mapGetters } from 'vuex';
import { getProductslist, getProductHot, storeCategory } from '@/api/store.js';
import { initiateAssistApi } from '@/api/activity.js';
import { setVisit, spread } from '@/api/user.js';
import recommend from '@/components/recommend';
// #ifndef H5
import passwordPopup from '@/components/passwordPopup';
// #endif
// #ifdef H5
import mConfig from './component/index.js';
// #endif
import customTab from '@/components/customTab';
// #ifdef MP || APP-PLUS
import activeParty from './component/activeParty';
import headerSerch from './component/headerSerch';
import homeComb from './component/homeComb';
import coupon from './component/coupon';
import bargain from './component/bargain';
import blankPage from './component/blankPage';
import combination from './component/combination';
import customerService from './component/customerService';
import goodList from './component/goodList';
import guide from './component/guide';
import liveBroadcast from './component/liveBroadcast';
import menus from './component/menus';
import news from './component/news';
import topic from './component/topic';
import presellList from './component/presellList';
import plantList from './component/plantList';
import pictureCube from './component/pictureCube'
import shopList from './component/shopList';
import richText from './component/richText';
import seckill from './component/seckill';
import swiperBg from './component/swiperBg';
import tabNav from './component/tabNav';
import titles from './component/titles';
import hotRanking from "./component/hotRanking.vue";
// #endif
import { silenceBindingSpread, configMap, isWeixin } from '@/utils/index';
import history from '@/mixins/history';
import shareScence from '@/libs/spread';
import easyLoadimage from '@/components/easy-loadimage/easy-loadimage.vue';
import {getNewPeopleCouponLst} from '@/api/activity.js'
import { HTTP_REQUEST_URL } from '@/config/app';
export default {
	computed: configMap({hide_mer_status:0,recommend_switch:0,beian_sn:'',share_title:'',share_pic:'',site_name:'',hot_ranking_switch:0,navigation: {}},
	mapGetters(['isLogin','uid','keyColor','viewColor','cartNum'])),
	mixins: [history],
	components: {
		easyLoadimage,
		recommend,
		customTab,
		// #ifndef H5
		passwordPopup,
		// #endif
		// #ifdef H5
		...mConfig,
		// #endif
		// #ifdef MP || APP-PLUS
		activeParty,
		headerSerch,
		homeComb,
		bargain,
		coupon,
		blankPage,
		combination,
		customerService,
		goodList,
		guide,
		liveBroadcast,
		menus,
		news,
		topic,
		presellList,
		plantList,
		pictureCube,
		shopList,
		richText,
		seckill,
		swiperBg,
		tabNav,
		titles,
		hotRanking
		// #endif
	},
	data() {
		return {
			domain: HTTP_REQUEST_URL,
			couponTypeMsg:{
				10: '通用券',
				11: '品类券',
				12: '跨店券'
			},
			styleConfig: [],
			showSkeleton: true, //骨架屏显示隐藏
			isNodes: 0, //控制什么时候开始抓取元素节点,只要数值改变就重新抓取
			privacyStatus: false,
			errorNetwork: false,
			userInfo: {},
			loading: false,
			statusBarHeight: statusBarHeight,
			navIndex: 0,
			navTop: [],
			subscribe: false,
			followUrl: '',
			followHid: true,
			followCode: false,
			sortList: [],
			window: false,
			navH: '',
			couponList: [],
			marTop: 0,
			loadend: false,
			loading: false,
			loadTitle: '加载更多',
			sortProduct: [],
			where: {
				pid: 0,
				page: 1,
				limit: 6
			},
			is_switch: true,
			hostProduct: [],
			hotPage: 1,
			hotLimit: 30,
			hotScroll: true,
			hotLoading: false,
			hotTitle: '加载更多',
			// #ifdef MP || APP-PLUS
			isFixed: false,
			// #endif
			// #ifdef H5
			isFixed: false,
			// #endif
			isScrolled: false,
			scrolled: false,
			tempArr: [], //精品推荐临时数组
			d: '',
			h: '',
			m: '',
			s: '',
			sum_h: '',
			sortMarTop: 0,
			globalDatas: {},
			currSpid: '',
			_options:{},
			isIntegral: false,
			appUpdate: {},
			bgColor: '',
			bgPic: '',
			bgTabVal: '',
			windowHeight: 0,
			domHeight: 0,
			isHeaderSerch: false,
			showCoupon: false,
			couponArray: [
				// {coupon_price:"200",use_min_price:"1000"},
				// {coupon_price:"200",use_min_price:""},
				// {coupon_price:"20",coupon_type:"balance"},
			],
			register_popup_pic: "",
			register_give_integral: 0,
			balance: 0,
			smallPage: false,
			isMenu: false,
			isScale: false,
			homeCombData: {},
			footerStatus: false,	
			newData: {},
			activeRouter: '',
			diyId: 0,
			preview: false,
			isSupport: true,
			only_register_money: false,
			isFooter: false,
		};
	},
	/**
	 * 用户点击右上角分享
	 */
	// #ifdef MP
	onShareAppMessage: function() {
		let that = this;
		wx.showShareMenu({
			withShareTicket: true,
			menus: ['shareAppMessage', 'shareTimeline']
		});
		return {
			title: that.share_title,
			imageUrl: that.share_pic,
			path: '/pages/index/index?spid=' + that.uid
		};
	},
	onShareTimeline: function() {
		let that = this;
		return {
			title: that.share_title,
			query: {
				spid: that.uid
			},
			imageUrl: that.share_pic
		};
	},
	// #endif
	onLoad(options) {
		let that = this
		this._options = options;
		this.diyId = options.diyId || 0;
		this.preview = options.time ? true : false;
		that.$nextTick(function() {
			uni.getSystemInfo({
				success: function(res) {
					that.windowHeight = res.windowHeight;
				}
			});
		})
		// #ifdef APP-PLUS
		that.appVersionConfig(); //APP版本检测
		that.checkFirstLaunch(options);
		// #endif
		// #ifndef APP-PLUS
		that.pageLoad(options);
		// #endif
		
		// uni.onNetworkStatusChange((res)=>{
		// 	if(res.isConnected){
		// 		that.pageLoad(options);
		// 	}
		// })
	},
	created(options) {
		let routes = getCurrentPages(); // 获取当前打开过的页面路由数组
		let curRoute = routes[routes.length - 1].route //获取当前页面路由
		this.activeRouter = '/' + curRoute
	},
	onShow() {
		let that = this
		that.isIntegral = uni.getStorageSync('isIntegral')
		// #ifdef APP-PLUS
		if(that.appUpdate.openUpgrade == '1'){
			that.alertAppUpdate();
		}
		// #endif
		that.getNav()
		that.loadCoupon()
		that.$nextTick(function() {
			const query = uni.createSelectorQuery();
			query.select('#home').boundingClientRect(); //获取当前元素距离顶部的距离
			query.exec(res =>{
				that.isScrolled = res[0].top <= -60
			})
		})	
		that.$util.getCartNum(that.isLogin,null)	
	},
	onHide() {
		uni.setStorageSync('isIntegral',false)
	},
	onReady() {},
	watch: {
		globalDatas(nVal, oVal) {
			// #ifdef H5
			this.ShareInfo(nVal);
			// #endif
		},
	},
	onPullDownRefresh(){
		this.reconnect();
		// #ifdef APP-PLUS
		setTimeout(() => {
			uni.reLaunch({
				url: "/pages/index/index"
			});	
		}, 50)
		// #endif
	},
	methods: {
		goCopyright(url){
			this.$util.JumpPath(url);	
		},
		loadCoupon(){
			if(!this.showCoupon && !uni.getStorageSync('show_coupon') && uni.getStorageSync('is_new_user')){
				getNewPeopleCouponLst().then(res => {
					this.couponArray = res.data.coupon;
					this.only_register_money = res.data.coupon.length >0 ? false : true;
					if(res.data.register_money_status==1 && res.data.register_give_money>0){
						this.couponArray.push({
							coupon_price: res.data.register_give_money,
							coupon_type: "balance",
						})
					}
					this.register_give_integral = res.data.register_integral_status == 1 ? res.data.register_give_integral : "";
					this.register_popup_pic = res.data.register_popup_pic;
					setTimeout(() => {
						this.showCoupon = res.data.newcomer_status == 1 && (res.data.coupon.length > 0 || (res.data.register_integral_status&&res.data.register_give_money>0));
						uni.setStorageSync('show_coupon', true)
					}, 1500);
				})
			}
		},	
		goCoupon(){
			if(this.register_popup_pic){
				uni.navigateTo({
					url: '/pages/columnGoods/goods_coupon_list/index'
				});
			}
		},
		checkFirstLaunch(options) {
		    const isFirstLaunch = !uni.getStorageSync('appLaunched');
		    if (isFirstLaunch) {
		      uni.setStorageSync('appLaunched', true);
		      this.showWelcomeAndCheckNetwork(options);
		    } else {
		      this.pageLoad(options);
		    }
		  }, 
		  showWelcomeAndCheckNetwork(options) {
		    uni.showModal({
		      title: '欢迎',
		      content: '首次使用请确保网络连接正常',
		      success: (res) => {
						debugger
		        if (res.confirm) {
		          this.pageLoad(options);
		        }
		      }
		    });
		  },
		async pageLoad(options){
			let that = this;
			if (options.spid) {
				that.currSpid = options.spid;
				app.globalData.spid = options.spid;
			}else if(getStorage('spid')){
				that.currSpid = getStorage('spid');
				app.globalData.spid = getStorage('spid');
			}
			// #ifdef APP-PLUS
			try {
				let val = uni.getStorageSync('privacyStatus') || true;
				if (!val) {
					this.privacyStatus = true;
				}
			} catch (e) {}
			this.snycNetWork();
			// #endif
			// #ifdef MP
			if (options.scene) {
				let value = that.$util.getUrlParams(decodeURIComponent(options.scene));
				if (value.id) options.id = value.id;
				//记录推广人uid
				if (value.spid) {
					that.currSpid = value.spid;
					app.globalData.spid = value.spid;
				}else if(getStorage('spid')){
					that.currSpid = getStorage('spid');
					app.globalData.spid = getStorage('spid');
				}
			}
			// #endif
			shareScence(that.currSpid, that.isLogin);
			this.isLogin && silenceBindingSpread();
			this.reloadData();
			Promise.all([
				this.diyData(0),
			]);
			if (this.isLogin) {
				this.getUserInfo();
			}
		},
		closeShare(){
			uni.setStorageSync('isIntegral',false);
			this.isIntegral = uni.getStorageSync('isIntegral');
		},
		// 重新链接
		reconnect() {
			uni.getNetworkType({
				success: res => {
					this.errorNetwork = res.networkType === 'none';
					if(!this.errorNetwork){
						this.pageLoad(this._options);
					}
				}
			});
		},
		getNav(){
			getNavigation({pid: this.currSpid}).then(res => {
				this.newData = res.data
				if (this.newData.status && this.newData.status.status) {
					uni.hideTabBar()
					this.isFooter = true
				} else {
					uni.showTabBar()
					this.isFooter = false
				}
			})
		},
		// #ifdef APP-PLUS
		snycNetWork() {
			uni.getNetworkType({
				success: res => {
					this.errorNetwork = res.networkType === 'none';
				}
			});
		},
		// 同意隐私协议
		confirmApp() {
			uni.setStorageSync('privacyStatus', true);
			this.privacyStatus = false;
		},
		// 关闭 Model
		closeModel() {
			this.privacyStatus = false;
		},
		// #endif
		// 对象转数组
		objToArr(data) {
			let obj = Object.keys(data).sort();
			let m = obj.map(key => data[key]);
			return m;
		},
		diyData(id) {
			let that = this;
			let parmas = {
				id: id,
				did: that.diyId, 
				version: '221'
			}
			that.styleConfig = []
			that.smallPage = false;
			getDiy(parmas).then(res => {
				setTimeout(() => {
					that.isNodes++;
				}, 0);
				that.errorNetwork = false
				let data = res.data;
				uni.setNavigationBarTitle({
					title: data.title
				});			
				if (data.is_bg_color) {
					that.bgColor = data.color_picker
				}
				if (data.is_bg_pic) {
					that.bgPic = data.bg_pic
					that.bgTabVal = data.bg_tab_val
				}else{
					that.bgPic = ""
				}
				that.styleConfig = that.objToArr(data.value);
				that.styleConfig.forEach((item, index, arr) => {
					item.did = data.id
					if (item.name == 'headerSerch' || item.name == 'homeComb') {
						that.isHeaderSerch = true
						if(item.name == 'homeComb'){
							that.$set(that, 'homeCombData', item)
							that.smallPage = true;
						}
					}
					if (item.name == 'pageFoot') {
						if (item.status && item.status.status) {
							this.newData = item
							setTimeout((e) => {
								that.$set(that, 'footerStatus', true);
							}, 50)
						}
						uni.setStorageSync('FOOTER_BAR', item.status && item.status.status ? true : false)
						item.menuList.map((path, index) => {
							if (path.link === '/pages/order_addcart/order_addcart') {
								uni.setStorageSync('FOOTER_ADDCART', index)
							}
						})
						arr.splice(index, 1);
					}
					if(item.name == 'menus'){
						that.isMenu = true;
					}
					if(that.recommend_switch == 1)that.get_host_product()
					uni.pageScrollTo({
						scrollTop: 0,
					})
					that.overflow = false;
					uni.stopPullDownRefresh();
				});
			}).catch(err => {
				uni.showToast({
					title: err,
					icon: 'none'
				})
				uni.stopPullDownRefresh();
			});
		},
		changeDiy(id) {
			let that = this;
			if(id){
				that.styleConfig = []
				getPageDiy({id: id,version:'221'}).then(res => {
					let data = res.data
					uni.setNavigationBarTitle({
						title: data.title
					});
					that.isScale = true
					if (data.is_bg_color) {
						that.bgColor = data.color_picker
					}
					if (data.is_bg_pic) {
						that.bgPic = data.bg_pic
						that.bgTabVal = data.bg_tab_val
					}else{
						that.bgPic = ""
					}
					that.styleConfig = that.objToArr(data.value);
					that.styleConfig.forEach((item, index, arr) => {
						item.did = data.id
						if (item.name == 'headerSerch' || item.name == 'homeComb' ||  item.name == 'tabNav') {
							that.styleConfig.splice(index, 1);
						}
						if(item.name == 'menus'){
							that.isMenu = true;
						}
					});
					that.isHeaderSerch = true
					that.isScale = true
					uni.pageScrollTo({
						scrollTop: 0,
					})
					uni.stopPullDownRefresh();
				}).catch(err => {
					that.styleConfig = []
					uni.stopPullDownRefresh();
					uni.showToast({
						title: err,
						icon: 'none'
					})
				});
			}else{
				that.diyData(0)
				that.isScale = false
			}
		},
		// #ifdef APP-PLUS
		appVersionConfig() {
			var that = this;
			//app升级
			// 获取本地应用资源版本号
			getAppVersion().then(res => {
				that.$set(that.appUpdate, 'androidAddress', res.data.androidAddress);
				that.$set(that.appUpdate, 'appVersion', res.data.appVersion);
				that.$set(that.appUpdate, 'iosAddress', res.data.iosAddress);
				that.$set(that.appUpdate, 'openUpgrade', res.data.openUpgrade);
				plus.runtime.getProperty(plus.runtime.appid, function(inf) {
					let nowVersion = (inf.version).split('.').join('');
					let appVersion = (res.data.appVersion).split('.').join('');
					that.$set(that.appUpdate, 'alert', appVersion > nowVersion);
					that.alertAppUpdate();
				});
			})
		},
		alertAppUpdate(){
			if(this.appUpdate.alert) {
				uni.getSystemInfo({
					success: (res) => {
						uni.showModal({
							title: '更新提示',
							content: '发现新版本，是否前去下载?',
							showCancel: this.appUpdate.openUpgrade != '1',
							cancelColor: '#eeeeee',
							confirmColor: '#FF0000',
							success:(response)=> {
								if (response.confirm) {
									switch (res.platform) {
										case "android":
											plus.runtime.openURL(this
												.appUpdate
												.androidAddress);
											break;
										case "ios":
											plus.runtime.openURL(encodeURI(
												this.appUpdate
												.iosAddress));
											break;
									}
								}
							}
						});
					}
				})
			}
		},
		// #endif
		// 分类页更多
		bindMore() {
			try {
				uni.setStorageSync('storeIndex', this.navTop[this.navIndex].pid);
				uni.switchTab({
					url: '/pages/goods_cate/goods_cate'
				});
			} catch (e) {}
		},
		goDetails(item) {
			goShopDetail(item, this.uid).then(res => {
				if (this.isLogin) {
					initiateAssistApi(item.activity_id)
						.then(res => {
							let id = res.data.product_assist_set_id;
							uni.hideLoading();
							uni.navigateTo({
								url: '/pages/activity/assist_detail/index?id=' + id
							});
						})
						.catch(err => {
							uni.showToast({
								title: err,
								icon: 'none'
							});
						});
				} else {
					toLogin()
				}
			});
		},
		/**
		 * 获取个人用户信息
		 */
		getUserInfo: function() {
			let that = this;
			getUserInfo().then(res => {
				that.userInfo = res.data;
			});
		},
		// 记录会员访问
		setVisit() {
			setVisit({
				url: '/pages/index/index'
			}).then(res => {
				console.log(res);
			});
		},
		// 导航分类切换
		changeTab(e,tabTitle) {
			let self = this;
			if (this.navIndex == e.index) return;
			this.navIndex = e.index;
			if (e.index > 0) {
				storeCategory({
					pid: e.pid
				}).then(res => {
					this.sortList = res.data.length > 9 ? res.data.splice(0, 9) : res.data;
					if (this.sortList.length > 0) {
						this.where.pid = e.pid;
						this.where.page = 1;
						this.loadend = false;
						this.loading = false;
						this.sortProduct = [];
						this.navTop = tabTitle;
						this.get_product_list();
						uni.pageScrollTo({
							scrollTop: 0,
						})
					}
				});
				self.sortMarTop = 10;
			}
		},
		//分类产品
		get_product_list: function() {
			let that = this;
			if (that.loading || that.loadend) return;
			that.loading = true;
			that.loadTitle = '';
			getProductslist(that.where)
				.then(res => {
					let list = res.data.list;
					let productList = that.$util.SplitArray(list, that.sortProduct);
					let loadend = list.length < that.where.limit;
					that.loadend = loadend;
					that.loading = false;
					that.loadTitle = loadend ? '已全部加载' : '加载更多';
					that.$set(that, 'sortProduct', productList);
					that.$set(that.where, 'page', that.where.page + 1);
				})
				.catch(err => {
					that.loading = false;
					that.loadTitle = '加载更多';
				});
		},
		/**
		 * 获取我的推荐
		 */
		get_host_product: function() {
			let that = this;
			let num = that.hotLimit;
			if (!that.hotScroll) return;
			if (that.hotLoading) return;
			that.hotLoading = true;
			that.hotTitle = '';
			getProductHot(that.hotPage, that.hotLimit).then(res => {
				let list = res.data.list;
				let productList = that.$util.SplitArray(list, that.hostProduct);
				let hotScroll = list.length <= num && list.length != 0;
				that.hotScroll = hotScroll;
				that.hotLoading = false;
				that.hotTitle = !hotScroll ? '已全部加载' : '加载更多';
				that.$set(that, 'hostProduct', productList);
				that.$set(that, 'hotPage', that.hotPage + 1);
			});
		},
		reloadData() {
			setTimeout(() => {
				this.showSkeleton = false
			}, 500)
		},
		// 首发新品详情
		goDetail(item) {
			if(item.cate_hot == 1){
				uni.navigateTo({
					url: '/pages/goods_recommend/index?id=' + item.product_id
				});		
			}else{
				if (item.activity && item.activity.type === '2' && !this.isLogin) {
					// #ifdef H5
					uni.showModal({
						title: '提示',
						content: '您未登录，请登录！',
						success: function(res) {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pages/users/login/index'
								});
							} else if (res.cancel) {
							}
						}
					});
					// #endif
					// #ifdef MP
					toLogin()
					// #endif
					return;
				} else {
					goShopDetail(item, this.uid).then(res => {
						if (this.isLogin) {
							initiateAssistApi(item.activity_id)
								.then(res => {
									let id = res.data.product_assist_set_id;
									uni.hideLoading();
									uni.navigateTo({
										url: '/pages/activity/assist_detail/index?id=' + id
									});
								})
								.catch(err => {
									uni.showToast({
										title: err,
										icon: 'none'
									});
								});
						} else {
							toLogin()
						}
					});
				}
			}
		},
		//#ifdef H5
		ShareInfo(datas) {
			let data = this.storeInfo;
			let href = location.href;
			if (this.$wechat.isWeixin()) {
				if (this.isLogin) {
					href = href.indexOf('?') === -1 ? href + '?spid=' + this.uid : href + '&spid=' + this.uid;
				} else {
					href = href;
				}
				let configAppMessage = {
					desc: datas.share_info,
					title: datas.share_title,
					link: href,
					imgUrl: datas.share_pic
				};
				this.$wechat
					.wechatEvevt(['updateAppMessageShareData', 'updateTimelineShareData', 'onMenuShareAppMessage', 'onMenuShareTimeline'], configAppMessage)
					.then(res => {})
					.catch(err => {
						console.log(err);
					});
			}
		},
		//#endif
	},
	mounted() {
		let query = uni.createSelectorQuery().in(this);
		query.select("#home").boundingClientRect();
		query.exec(res => {
			this.domHeight = res[0].height;
		})
	},
	// 滚动到底部
	onReachBottom() {
		if(this.recommend_switch == 1 && this.navIndex == 0){
			// 首页加载更多
			this.get_host_product();
		}else if(this.navIndex == 1){
			// 分类栏目加载更多
			if (this.sortProduct.length > 0) {
				this.get_product_list();
			}
		}
	},
	// 滚动监听
	onPageScroll(e) {
		this.isFixed = e.scrollTop > 40;
		if (this.isHeaderSerch) {
			let isScroll = false
			isScroll = e.scrollTop > 50
			this.isScrolled = isScroll;
		} else {
			this.isScrolled = false
		}
		// 传入scrollTop值并触发所有easy-loadimage组件下的滚动监听事件
		uni.$emit('scroll');
	},
	onUnload() {
		// 页面卸载时取消监听
		uni.offNetworkStatusChange();
	}
};
</script>
<style lang="scss" scoped>
page {
	display: flex;
	flex-direction: column;
}
/deep/ .home-comb .my-main{
	left: 0;
	position: fixed;
	top: 0;
	width: 100%;
	z-index: 30;
}
.colum0{
	white-space: nowrap; 
	display: flex;
}
.ysize {
	background-size: 100%;
}
.fullsize {
	background-size: 100% 100%;
}
.repeat {
	background-repeat: repeat;
}
.noRepeat {
	background-repeat: no-repeat;
}
.privacy-wrapper {
	z-index: 999;
	position: fixed;
	left: 0;
	top: 0;
	bottom: 0;
	right: 0;
	width: 100%;
	height: 100%;
	background: #7f7f7f;
	.privacy-box {
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		width: 560rpx;
		padding: 50rpx 45rpx 0;
		background: #fff;
		border-radius: 20rpx;
		.title {
			text-align: center;
			font-size: 32rpx;
			text-align: center;
			color: #333;
			font-weight: 700;
		}
		.content {
			margin-top: 20rpx;
			line-height: 1.5;
			font-size: 26rpx;
			color: #666;
			navigator {
				display: inline-block;
				color: #e93323;
			}
		}
		.btn-box {
			margin-top: 40rpx;
			text-align: center;
			font-size: 30rpx;
			.btn-item {
				height: 82rpx;
				line-height: 82rpx;
				background: linear-gradient(90deg, #f67a38 0%, #f11b09 100%);
				color: #fff;
				border-radius: 41rpx;
			}
			.btn {
				padding: 30rpx 0;
			}
		}
	}
}
.coupon_popups{
	z-index: 999;
	position: fixed;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	text-align: center;
	.bg{
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0,0,0,.5);
	}
	.con{
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%,-50%);
		width: 540rpx;
		height:860rpx;
		background-size: 100% auto;
		background-repeat: no-repeat;
		.coupon-count{
			width: 464rpx;
			max-height: 300rpx;
			padding: 20rpx 10rpx 0;
			margin: 180rpx auto 0;
		}
		.item{
			display: flex;
			align-items: center;
			width: 100%;
			height: 140rpx;
			background-repeat: no-repeat;
			background-size: 100% 100%;
			margin-bottom: 28rpx;
			border-radius: 20rpx;
			position: relative;
			.left{
				width: 240rpx;
				text-align: center;
				.price{
					color: #FBFCDA;
					font-size: 26rpx;
					font-weight: bold;
					text{
						font-size: 46rpx;
					}
				}
				.max_price{
					color: rgba(251,252,218,0.8);
					font-size: 18rpx;
					margin-top: 8rpx;
				}
			}
			.right{
				width: 432rpx;
				padding: 0 20rpx;
				.title{
					color: #FBFCDA;
					font-size: 28rpx;
					font-weight: bold;
				}
				.info{
					color: rgba(255,249,216,0.8);
					font-size: 16rpx;
					margin-top: 12rpx;
				}
			}
		}
		.integral-count{
			color: #3D3D3D;
			font-size: 18rpx;
			margin-top: 40rpx;
		}
		.coupon-window-btn{
			width: 100%;
			position: absolute;
			bottom: 0;
			.coupon-btn{
				width: 442rpx;
				height: 106rpx;
				background-size: 100% 100%;
				margin: 0 auto 60rpx;
			}
			.icon-ic_close1{
				color: #ffffff;
				font-size: 70rpx;
				margin: 0 auto;
			}
		}	
	}
}
.item_tags{
	margin-top: 8rpx;
	display: flex;
}
.item_tags .tags_item {
	display: inline-block;
	font-size: 20rpx;
	text-align: center;
	border-radius: 5rpx;
	padding: 0 4rpx;
	line-height: 28rpx;
	margin-right: 8rpx;
}
.item_tags .tags_item.ticket{
	color: var(--view-theme);
	border: 1px solid var(--view-theme);
}
.item_tags .tags_item.delivery{
	color: #FF9000;
	border: 1px solid #FF9000;
}
.copyRight{
	margin: 60rpx auto 20rpx;
	max-width: 690rpx;
	font-size: 20rpx;
	color: #707070;
	text-decoration: none;
}
.error-network {
	position: fixed;
	left: 0;
	top: 0;
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 100%;
	height: 100%;
	padding-top: 40rpx;
	background: #fff;
	padding-top: 30%;
	image {
		width: 414rpx;
		height: 336rpx;
	}
	.title {
		position: relative;
		top: -40rpx;
		font-size: 32rpx;
		color: #666;
	}
	.con {
		font-size: 24rpx;
		color: #999;
		.label {
			margin-bottom: 20rpx;
		}
		.item {
			margin-bottom: 20rpx;
		}
	}
	.btn {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 508rpx;
		height: 86rpx;
		margin-top: 100rpx;
		border: 1px solid #d74432;
		color: #e93323;
		font-size: 30rpx;
		border-radius: 120rpx;
	}
}
.area-row {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	display: block;
	width: 100%;
	text-align: center;
}
.page-index {
	display: flex;
	flex-direction: column;
	min-height: 100%;
}
.productList {
	background-color: #f1f1f1;
	min-height: 70vh;
	.sort {
		width: 710rpx;
		max-height: 380rpx;
		background: #ffffff;
		border-radius: 24rpx;
		padding: 8rpx 0rpx 30rpx;
		flex-wrap: wrap;
		margin: 25rpx auto 0 auto;
		.item {
			width: 20%;
			margin-top: 30rpx;
			text-align: center;
			position: relative;
			.pictrues {
				width: 82rpx;
				height: 82rpx;
				background: rgba(248, 248, 248, 1);
				border-radius: 100%;
				margin: 0 auto;
			}
			.easy-loadimage {
				width: 90rpx;
				height: 90rpx;
				display: inline-block;
			}
			.text {
				color: #282828;
				font-size: 24rpx;
				margin-top: 10rpx;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
		}
	}
}
.productList .list {
	padding: 0 20rpx;
}
.productList .list .item {
	width: 345rpx;
	margin-top: 20rpx;
	background-color: #fff;
	border-radius: 24rpx;
	position: relative;
	.name{
		display: flex;
		align-items: center;
		height: 70rpx;
	}
	.add-cart {
		bottom: 20rpx;
		right: 20rpx;
		background: #E93323;
	}
}
.productList .list .item .pictrue {
	position: relative;
	width: 100%;
	height: 345rpx;
}
/deep/.productList .list .item .pictrue image,
/deep/.productList .list .item .pictrue uni-image,
/deep/.productList .list .item .pictrue .easy-loadimage{
	width: 100%;
	height: 100%;
	border-radius: 24rpx 24rpx 0 0;
}
/deep/.productList .list .item .pictrue uni-image.origin-img{
	border-radius: 24rpx 24rpx 0 0;
}
.productList .list .item .pictrue image.on {
	border-radius: 6rpx;
}
.productList .list .item .text {
	padding: 14rpx 17rpx 26rpx 17rpx;
	font-size: 28rpx;
	color: #212121;
}
.productList .list .item .text .money {
	font-size: 26rpx;
	font-weight: bold;
	margin-top: 16rpx;
	color: #E93323;
}
.pictrue {
	position: relative;
	.border-picture {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		border-radius: 24rpx 24rpx 0 0;
		background: center/cover no-repeat;
	}
}
.cate_pictrue,/deep/.cate_pictrue image,/deep/.cate_pictrue .easy-loadimage,/deep/.cate_pictrue uni-image{
	width: 82rpx;
	height: 82rpx;
	margin: 0 auto;
}
.fixed {
	z-index: 100;
	position: fixed;
	left: 0;
	top: 0;
	background-color: #fff;
	box-shadow: 0 10rpx 20rpx -5rpx rgba(0, 0, 0, 0.06);
}

</style>
