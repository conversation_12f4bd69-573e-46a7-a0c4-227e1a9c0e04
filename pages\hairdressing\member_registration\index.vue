<template>
  <view class="container">
    <view class="banner">
      <image
        class="banner__img"
        src="http://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/f863a202507141001466099.png"
        mode="widthFix"
      />
    </view>
    <!-- 注册表单 -->
    <view class="form-card">
      <view class="form-item">
        <text class="label">电话</text>
        <view class="phone-group">
          <input
            class="input phone-input"
            type="number"
            maxlength="11"
            v-model="phone"
            placeholder="请输入手机号"
          />
          <button class="verify-btn" :disabled="disabled" @click="handleVerify">
            {{ text }}
          </button>
        </view>
      </view>
      <view class="form-item">
        <text class="label">验证码</text>
        <input
          class="input"
          type="number"
          maxlength="6"
          v-model="captcha"
          placeholder="请输入验证码"
        />
      </view>
      <view class="form-item">
        <text class="label">请选择性别</text>
        <view class="gender-group">
          <view
            :class="['gender-btn', gender === '男' ? 'active' : '']"
            @click="handleCheckGender('男')"
          >
            男
          </view>
          <view
            :class="['gender-btn', gender === '女' ? 'active' : '']"
            @click="handleCheckGender('女')"
          >
            女
          </view>
        </view>
      </view>
      <view class="form-item">
        <text class="label">生日</text>
        <picker mode="date" :value="birthday" @change="onDateChange">
          <view class="input date-input">
            {{ birthday ? birthday : '请选择日期' }}
          </view>
        </picker>
      </view>
      <view class="btn-group">
        <button hover-class="button-hover" class="btn cancel" @click="onCancel">取消注册</button>
        <button hover-class="button-hover" class="btn confirm" @click="onRegister">注册会员</button>
      </view>
    </view>
    <Verify @success="success" :imgSize="{ width: '330px', height: '155px' }" ref="verify"></Verify>
  </view>
</template>

<script>
import sendVerifyCode from '@/mixins/SendVerifyCode'
import Verify from '@/components/verify/verify.vue'
import { registerVerify } from '@/api/user.js'
import { bindingPhone } from '@/api/api.js'
import { getUserSetting, userSettingEdit } from '@/api/user.js'

export default {
  mixins: [sendVerifyCode],
  components: {
    Verify,
  },
  data() {
    return {
      phone: '',
      captcha: '',
      gender: '',
      birthday: '',
      counting: 0,
      timer: null,
      userInfo: {},
    }
  },
  computed: {
    canSendCode() {
      return this.phone && /^1\d{10}$/.test(this.phone)
    },
  },
  onLoad(options) {
    this.getUserSetting()
  },
  methods: {
    /**
     * 获取用户详情
     */
    getUserSetting() {
      getUserSetting().then((res) => {
        this.$set(this, 'userInfo', res.data)
      })
    },
    handleCheckGender(gender) {
      this.gender = gender
      this.userInfo.extend_info.forEach((item) => {
        if (item.field === 'sex') {
          // 1 代表男，2 代表女，0 代表保密
          item.value = gender === '男' ? '1' : '2'
        }
      })
    },
    onDateChange(e) {
      this.birthday = e.detail.value
      this.userInfo.extend_info.forEach((item) => {
        if (item.field === 'birthday') {
          item.value = this.birthday
        }
      })
    },
    onCancel() {
      uni.navigateBack()
    },
    handleVerify() {
      this.$refs.verify.show()
    },
    onRegister() {
      //  先绑定手机号
      // 判断信息是否填写完整
      if (!this.phone) {
        return this.$util.Tips({
          title: '请填写手机号码！',
        })
      }
      if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(this.phone)) {
        return this.$util.Tips({
          title: '请输入正确的手机号码！',
        })
      }
      if (!this.captcha) {
        return this.$util.Tips({
          title: '请填写验证码',
        })
      }
      if (!this.gender) {
        return this.$util.Tips({
          title: '请选择性别',
        })
      }
      if (!this.birthday) {
        return this.$util.Tips({
          title: '请选择生日',
        })
      }
      this.editPwd()
    },
    success(data) {
      this.$refs.verify.hide()
      this.code(data)
    },
    /**
     * 发送验证码
     *
     */
    editPwd() {
      let that = this
      if (!that.phone)
        return that.$util.Tips({
          title: '请填写手机号码！',
        })
      if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.phone))
        return that.$util.Tips({
          title: '请输入正确的手机号码！',
        })
      if (!that.captcha)
        return that.$util.Tips({
          title: '请填写验证码',
        })
      bindingPhone({
        phone: that.phone,
        sms_code: that.captcha,
      })
        .then((res) => {
          that.formSubmit()
        })
        .catch((err) => {
          return that.$util.Tips({
            title: err,
          })
        })
    },
    async code(data) {
      let that = this
      if (!that.phone)
        return that.$util.Tips({
          title: '请填写手机号码！',
        })
      if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.phone))
        return that.$util.Tips({
          title: '请输入正确的手机号码！',
        })
      this.disabled = true
      await registerVerify({
        phone: that.phone,
        key: that.key,
        code: that.captcha,
        type: 'binding',
        captchaVerification: data.captchaVerification,
      })
        .then((res) => {
          this.disabled = false
          that.$util.Tips({
            title: res.message,
          })
          that.sendCode()
        })
        .catch((err) => {
          this.disabled = false
          return that.$util.Tips({
            title: err,
          })
        })
    },
    /**
     * 提交修改
     */
    formSubmit(e) {
      try {
        let that = this
        userSettingEdit(that.userInfo)
          .then((res) => {
            that.$util.Tips(
              {
                title: res.message,
                icon: 'success',
              },
              {
                tab: 3,
              },
            )
          })
          .catch((msg) => {
            return that.$util.Tips({
              title: msg || '保存失败，您并没有修改',
            })
          })
      } catch (error) {
        console.log('🚀 ~ formSubmit ~ error:', error)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(180deg, #fde7d1 0%, #fde7d1 7%, #fafafa 100%);
  padding: 0 30rpx 40rpx;
}
.banner {
  padding-top: 38rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  &__img {
    width: 290rpx;
    height: 324rpx;
  }
}
.form-card {
  background: #fff;
  border-radius: 50rpx;
  padding: 60rpx 30rpx;
  display: flex;
  flex-direction: column;
  gap: 44rpx;
}
.form-item {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  .label {
    color: #999;
    font-size: 28rpx;
    margin-bottom: 8rpx;
    font-weight: 400;
  }
  .input {
    background: #f5f5f5;
    border-radius: 40rpx;
    padding: 20rpx 30rpx;
  }
  .date-input {
    color: #999;
  }
}
.gender-group {
  display: flex;
  gap: 24rpx;
  .gender-btn {
    flex: 1;
    text-align: center;
    padding: 20rpx 0;
    border-radius: 40rpx;
    background: #f6f6f6;
    color: #333;
    font-size: 28rpx;
    font-weight: 500;
    &.active {
      background: #cba46a;
      //   color: #fff;
    }
  }
}
.btn-group {
  display: flex;
  gap: 32rpx;
  margin: 68rpx 0;
  .btn {
    flex: 1;
    text-align: center;
    padding: 20rpx 0;
    border-radius: 40rpx;
    font-size: 32rpx;
    font-weight: 500;
    background: #f6f6f6;
    color: #333;
    font-weight: 400;
    font-size: 30rpx;
    color: #333333;
    &.confirm {
      background: #cba46a;
      color: #fff;
    }
  }
}
.phone-group {
  position: relative;
  display: flex;
  align-items: center;
  gap: 20rpx;

  .phone-input {
    flex: 1;
  }

  .verify-btn {
    width: 200rpx;
    height: 72rpx;
    line-height: 72rpx;
    background: #cba46a;
    color: #fff;
    font-size: 24rpx;
    border-radius: 36rpx;
    padding: 0;
    margin: 0;

    &[disabled] {
      background: #ccc;
    }
  }
}
</style>
