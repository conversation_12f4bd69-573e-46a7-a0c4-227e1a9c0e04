<template>
  <view class="work-detail" v-if="works">
    <!-- 作品图片 -->
    <view class="work-detail__image-section">
      <swiper class="swiper" :current="current">
        <block v-for="item in works.files" :key="item">
          <swiper-item class="swiper-item">
            <image class="swiper-item__image" :src="item" mode="widthFix" />
          </swiper-item>
        </block>
      </swiper>
      <view class="work-detail__image-index">{{ current + 1 }}/{{ works.files.length }}</view>
    </view>

    <!-- 作品信息 -->
    <view class="work-detail__info">
      <view class="work-detail__title">{{ works.title }}</view>
      <view class="work-detail__tags">
        <block v-for="(tag, idx) in works.tags" :key="idx">
          <text class="work-detail__tag">{{ tag }}</text>
        </block>
      </view>
      <view class="work-detail__desc">{{ works.content }}</view>
    </view>

    <!-- 发型师信息与按钮 -->
    <view class="work-detail__footer ios-pb">
      <view class="work-detail__stylist">
        <image
          class="work-detail__stylist-avatar"
          :src="works.storeService.avatar"
          mode="widthFix"
        />
        <view class="work-detail__stylist-name">{{ works.storeService.nickname }}</view>
        <view class="">
          <RoleTag role="店长" />
        </view>
      </view>
      <button class="work-detail__btn center" hover-class="btnHoverClass">预约发型师</button>
    </view>
  </view>
</template>

<script>
import RoleTag from '@/components/roleTags/roleTags.vue'
export default {
  name: 'WorkDetail',
  components: { RoleTag },
  data() {
    return {
      current: 0,
      works: null,
    }
  },
  onLoad(options) {
    if (options.info) {
      this.works = JSON.parse(options.info)
    }
  },
}
</script>

<style lang="scss" scoped>
.work-detail {
  background: #fff;
  min-height: 100vh;
  &__image-section {
    position: relative;
    width: 100%;
    .swiper {
      height: 900rpx;
      &-item {
        width: 100%;
        height: 100%;
        &__image {
          width: 100%;
          height: 100%;
          border-radius: 0 0 30rpx 30rpx;
        }
      }
    }
  }

  &__image-index {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 40rpx;
    background: rgba(0, 0, 0, 0.6);
    color: #fff;
    font-weight: 400;
    font-size: 20rpx;
    color: #ffffff;
    border-radius: 16rpx;
    padding: 6rpx 10rpx;
  }

  &__info {
    padding: 20rpx;
  }
  &__title {
    font-weight: 500;
    font-size: 28rpx;
    color: #333333;
    margin-bottom: 10rpx;
  }
  &__tags {
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    gap: 10rpx;
  }
  &__tag {
    display: inline-block;
    font-weight: 400;
    font-size: 20rpx;
    color: #d19e58;
    background: #fff3e1;
    border-radius: 8rpx;
    padding: 10rpx;
  }
  &__desc {
    color: #888;
    font-size: 24rpx;
    margin-top: 4rpx;
    line-height: 1.6;
  }

  &__footer {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 16rpx 20rpx;
    background: #fff;
    border-top: 1rpx solid #f2f2f2;
    position: fixed;
    left: 0;
    bottom: 0;
  }
  &__stylist {
    display: flex;
    align-items: center;
    column-gap: 10rpx;
  }
  &__stylist-avatar {
    width: 68rpx;
    height: 68rpx;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 10rpx;
    background: #eee;
  }
  &__stylist-name {
    font-weight: 500;
    font-size: 26rpx;
    color: #333333;
  }
  &__btn {
    margin-left: 40rpx;
    flex: 1;
    height: 78rpx;
    background: #d19e58;
    border-radius: 39rpx;
    font-weight: 400;
    font-size: 30rpx;
    color: #ffffff;
  }
}
</style>
f
